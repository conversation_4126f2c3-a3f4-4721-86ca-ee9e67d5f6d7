"use strict";(self.webpackChunk_soak_gw_base=self.webpackChunk_soak_gw_base||[]).push([[898],{7525:(e,t,n)=>{n.d(t,{ZP:()=>d});var o={capture:!0,once:!0,passive:!0},r=function(){return"interactive"===document.readyState||"complete"===document.readyState},a=function(e,t){return!(!function(e){return document.readyState===e}(e)&&!r()||(t(e),0))},c=function(){return new Promise((function(e){a("domready",e)||document.addEventListener("DOMContentLoaded",(function(){e("domready")}),o)}))},i=function(){return new Promise((function(e){a("load",e)||window.addEventListener("load",(function(){e("load")}),o)}))},u={};Object.defineProperties(u,{state:{get:function(){return document.readyState}},loading:{get:function(){return new Promise((function(e){a("loading",e)||document.addEventListener("readystatechange",(function(){"loading"===document.readyState&&e("loading")}),o)}))}},interactive:{get:function(){return new Promise((function(e){a("interactive",e)||document.addEventListener("readystatechange",(function(){"interactive"===document.readyState&&e("interactive")}),o)}))}},complete:{get:function(){return new Promise((function(e){a("complete",e)||document.addEventListener("readystatechange",(function(){"complete"===document.readyState&&e("complete")}),o)}))}},window:{get:function(){return i()}},load:{get:function(){return i()}},domready:{get:function(){return c()}},dom:{get:function(){return c()}},ready:{get:function(){return r()}}});const d=u}}]);