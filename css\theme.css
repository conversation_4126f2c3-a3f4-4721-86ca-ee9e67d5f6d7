[data-aos][data-aos][data-aos-duration="50"],
body[data-aos-duration="50"] [data-aos] {
    transition-duration: 50ms
}

[data-aos][data-aos][data-aos-delay="50"],
body[data-aos-delay="50"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="50"].aos-animate,
body[data-aos-delay="50"] [data-aos].aos-animate {
    transition-delay: 50ms
}

[data-aos][data-aos][data-aos-duration="100"],
body[data-aos-duration="100"] [data-aos] {
    transition-duration: .1s
}

[data-aos][data-aos][data-aos-delay="100"],
body[data-aos-delay="100"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="100"].aos-animate,
body[data-aos-delay="100"] [data-aos].aos-animate {
    transition-delay: .1s
}

[data-aos][data-aos][data-aos-duration="150"],
body[data-aos-duration="150"] [data-aos] {
    transition-duration: .15s
}

[data-aos][data-aos][data-aos-delay="150"],
body[data-aos-delay="150"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="150"].aos-animate,
body[data-aos-delay="150"] [data-aos].aos-animate {
    transition-delay: .15s
}

[data-aos][data-aos][data-aos-duration="200"],
body[data-aos-duration="200"] [data-aos] {
    transition-duration: .2s
}

[data-aos][data-aos][data-aos-delay="200"],
body[data-aos-delay="200"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="200"].aos-animate,
body[data-aos-delay="200"] [data-aos].aos-animate {
    transition-delay: .2s
}

[data-aos][data-aos][data-aos-duration="250"],
body[data-aos-duration="250"] [data-aos] {
    transition-duration: .25s
}

[data-aos][data-aos][data-aos-delay="250"],
body[data-aos-delay="250"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="250"].aos-animate,
body[data-aos-delay="250"] [data-aos].aos-animate {
    transition-delay: .25s
}

[data-aos][data-aos][data-aos-duration="300"],
body[data-aos-duration="300"] [data-aos] {
    transition-duration: .3s
}

[data-aos][data-aos][data-aos-delay="300"],
body[data-aos-delay="300"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="300"].aos-animate,
body[data-aos-delay="300"] [data-aos].aos-animate {
    transition-delay: .3s
}

[data-aos][data-aos][data-aos-duration="350"],
body[data-aos-duration="350"] [data-aos] {
    transition-duration: .35s
}

[data-aos][data-aos][data-aos-delay="350"],
body[data-aos-delay="350"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="350"].aos-animate,
body[data-aos-delay="350"] [data-aos].aos-animate {
    transition-delay: .35s
}

[data-aos][data-aos][data-aos-duration="400"],
body[data-aos-duration="400"] [data-aos] {
    transition-duration: .4s
}

[data-aos][data-aos][data-aos-delay="400"],
body[data-aos-delay="400"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="400"].aos-animate,
body[data-aos-delay="400"] [data-aos].aos-animate {
    transition-delay: .4s
}

[data-aos][data-aos][data-aos-duration="450"],
body[data-aos-duration="450"] [data-aos] {
    transition-duration: .45s
}

[data-aos][data-aos][data-aos-delay="450"],
body[data-aos-delay="450"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="450"].aos-animate,
body[data-aos-delay="450"] [data-aos].aos-animate {
    transition-delay: .45s
}

[data-aos][data-aos][data-aos-duration="500"],
body[data-aos-duration="500"] [data-aos] {
    transition-duration: .5s
}

[data-aos][data-aos][data-aos-delay="500"],
body[data-aos-delay="500"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="500"].aos-animate,
body[data-aos-delay="500"] [data-aos].aos-animate {
    transition-delay: .5s
}

[data-aos][data-aos][data-aos-duration="550"],
body[data-aos-duration="550"] [data-aos] {
    transition-duration: .55s
}

[data-aos][data-aos][data-aos-delay="550"],
body[data-aos-delay="550"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="550"].aos-animate,
body[data-aos-delay="550"] [data-aos].aos-animate {
    transition-delay: .55s
}

[data-aos][data-aos][data-aos-duration="600"],
body[data-aos-duration="600"] [data-aos] {
    transition-duration: .6s
}

[data-aos][data-aos][data-aos-delay="600"],
body[data-aos-delay="600"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="600"].aos-animate,
body[data-aos-delay="600"] [data-aos].aos-animate {
    transition-delay: .6s
}

[data-aos][data-aos][data-aos-duration="650"],
body[data-aos-duration="650"] [data-aos] {
    transition-duration: .65s
}

[data-aos][data-aos][data-aos-delay="650"],
body[data-aos-delay="650"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="650"].aos-animate,
body[data-aos-delay="650"] [data-aos].aos-animate {
    transition-delay: .65s
}

[data-aos][data-aos][data-aos-duration="700"],
body[data-aos-duration="700"] [data-aos] {
    transition-duration: .7s
}

[data-aos][data-aos][data-aos-delay="700"],
body[data-aos-delay="700"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="700"].aos-animate,
body[data-aos-delay="700"] [data-aos].aos-animate {
    transition-delay: .7s
}

[data-aos][data-aos][data-aos-duration="750"],
body[data-aos-duration="750"] [data-aos] {
    transition-duration: .75s
}

[data-aos][data-aos][data-aos-delay="750"],
body[data-aos-delay="750"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="750"].aos-animate,
body[data-aos-delay="750"] [data-aos].aos-animate {
    transition-delay: .75s
}

[data-aos][data-aos][data-aos-duration="800"],
body[data-aos-duration="800"] [data-aos] {
    transition-duration: .8s
}

[data-aos][data-aos][data-aos-delay="800"],
body[data-aos-delay="800"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="800"].aos-animate,
body[data-aos-delay="800"] [data-aos].aos-animate {
    transition-delay: .8s
}

[data-aos][data-aos][data-aos-duration="850"],
body[data-aos-duration="850"] [data-aos] {
    transition-duration: .85s
}

[data-aos][data-aos][data-aos-delay="850"],
body[data-aos-delay="850"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="850"].aos-animate,
body[data-aos-delay="850"] [data-aos].aos-animate {
    transition-delay: .85s
}

[data-aos][data-aos][data-aos-duration="900"],
body[data-aos-duration="900"] [data-aos] {
    transition-duration: .9s
}

[data-aos][data-aos][data-aos-delay="900"],
body[data-aos-delay="900"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="900"].aos-animate,
body[data-aos-delay="900"] [data-aos].aos-animate {
    transition-delay: .9s
}

[data-aos][data-aos][data-aos-duration="950"],
body[data-aos-duration="950"] [data-aos] {
    transition-duration: .95s
}

[data-aos][data-aos][data-aos-delay="950"],
body[data-aos-delay="950"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="950"].aos-animate,
body[data-aos-delay="950"] [data-aos].aos-animate {
    transition-delay: .95s
}

[data-aos][data-aos][data-aos-duration="1000"],
body[data-aos-duration="1000"] [data-aos] {
    transition-duration: 1s
}

[data-aos][data-aos][data-aos-delay="1000"],
body[data-aos-delay="1000"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1000"].aos-animate,
body[data-aos-delay="1000"] [data-aos].aos-animate {
    transition-delay: 1s
}

[data-aos][data-aos][data-aos-duration="1050"],
body[data-aos-duration="1050"] [data-aos] {
    transition-duration: 1.05s
}

[data-aos][data-aos][data-aos-delay="1050"],
body[data-aos-delay="1050"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1050"].aos-animate,
body[data-aos-delay="1050"] [data-aos].aos-animate {
    transition-delay: 1.05s
}

[data-aos][data-aos][data-aos-duration="1100"],
body[data-aos-duration="1100"] [data-aos] {
    transition-duration: 1.1s
}

[data-aos][data-aos][data-aos-delay="1100"],
body[data-aos-delay="1100"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1100"].aos-animate,
body[data-aos-delay="1100"] [data-aos].aos-animate {
    transition-delay: 1.1s
}

[data-aos][data-aos][data-aos-duration="1150"],
body[data-aos-duration="1150"] [data-aos] {
    transition-duration: 1.15s
}

[data-aos][data-aos][data-aos-delay="1150"],
body[data-aos-delay="1150"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1150"].aos-animate,
body[data-aos-delay="1150"] [data-aos].aos-animate {
    transition-delay: 1.15s
}

[data-aos][data-aos][data-aos-duration="1200"],
body[data-aos-duration="1200"] [data-aos] {
    transition-duration: 1.2s
}

[data-aos][data-aos][data-aos-delay="1200"],
body[data-aos-delay="1200"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1200"].aos-animate,
body[data-aos-delay="1200"] [data-aos].aos-animate {
    transition-delay: 1.2s
}

[data-aos][data-aos][data-aos-duration="1250"],
body[data-aos-duration="1250"] [data-aos] {
    transition-duration: 1.25s
}

[data-aos][data-aos][data-aos-delay="1250"],
body[data-aos-delay="1250"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1250"].aos-animate,
body[data-aos-delay="1250"] [data-aos].aos-animate {
    transition-delay: 1.25s
}

[data-aos][data-aos][data-aos-duration="1300"],
body[data-aos-duration="1300"] [data-aos] {
    transition-duration: 1.3s
}

[data-aos][data-aos][data-aos-delay="1300"],
body[data-aos-delay="1300"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1300"].aos-animate,
body[data-aos-delay="1300"] [data-aos].aos-animate {
    transition-delay: 1.3s
}

[data-aos][data-aos][data-aos-duration="1350"],
body[data-aos-duration="1350"] [data-aos] {
    transition-duration: 1.35s
}

[data-aos][data-aos][data-aos-delay="1350"],
body[data-aos-delay="1350"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1350"].aos-animate,
body[data-aos-delay="1350"] [data-aos].aos-animate {
    transition-delay: 1.35s
}

[data-aos][data-aos][data-aos-duration="1400"],
body[data-aos-duration="1400"] [data-aos] {
    transition-duration: 1.4s
}

[data-aos][data-aos][data-aos-delay="1400"],
body[data-aos-delay="1400"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1400"].aos-animate,
body[data-aos-delay="1400"] [data-aos].aos-animate {
    transition-delay: 1.4s
}

[data-aos][data-aos][data-aos-duration="1450"],
body[data-aos-duration="1450"] [data-aos] {
    transition-duration: 1.45s
}

[data-aos][data-aos][data-aos-delay="1450"],
body[data-aos-delay="1450"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1450"].aos-animate,
body[data-aos-delay="1450"] [data-aos].aos-animate {
    transition-delay: 1.45s
}

[data-aos][data-aos][data-aos-duration="1500"],
body[data-aos-duration="1500"] [data-aos] {
    transition-duration: 1.5s
}

[data-aos][data-aos][data-aos-delay="1500"],
body[data-aos-delay="1500"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1500"].aos-animate,
body[data-aos-delay="1500"] [data-aos].aos-animate {
    transition-delay: 1.5s
}

[data-aos][data-aos][data-aos-duration="1550"],
body[data-aos-duration="1550"] [data-aos] {
    transition-duration: 1.55s
}

[data-aos][data-aos][data-aos-delay="1550"],
body[data-aos-delay="1550"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1550"].aos-animate,
body[data-aos-delay="1550"] [data-aos].aos-animate {
    transition-delay: 1.55s
}

[data-aos][data-aos][data-aos-duration="1600"],
body[data-aos-duration="1600"] [data-aos] {
    transition-duration: 1.6s
}

[data-aos][data-aos][data-aos-delay="1600"],
body[data-aos-delay="1600"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1600"].aos-animate,
body[data-aos-delay="1600"] [data-aos].aos-animate {
    transition-delay: 1.6s
}

[data-aos][data-aos][data-aos-duration="1650"],
body[data-aos-duration="1650"] [data-aos] {
    transition-duration: 1.65s
}

[data-aos][data-aos][data-aos-delay="1650"],
body[data-aos-delay="1650"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1650"].aos-animate,
body[data-aos-delay="1650"] [data-aos].aos-animate {
    transition-delay: 1.65s
}

[data-aos][data-aos][data-aos-duration="1700"],
body[data-aos-duration="1700"] [data-aos] {
    transition-duration: 1.7s
}

[data-aos][data-aos][data-aos-delay="1700"],
body[data-aos-delay="1700"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1700"].aos-animate,
body[data-aos-delay="1700"] [data-aos].aos-animate {
    transition-delay: 1.7s
}

[data-aos][data-aos][data-aos-duration="1750"],
body[data-aos-duration="1750"] [data-aos] {
    transition-duration: 1.75s
}

[data-aos][data-aos][data-aos-delay="1750"],
body[data-aos-delay="1750"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1750"].aos-animate,
body[data-aos-delay="1750"] [data-aos].aos-animate {
    transition-delay: 1.75s
}

[data-aos][data-aos][data-aos-duration="1800"],
body[data-aos-duration="1800"] [data-aos] {
    transition-duration: 1.8s
}

[data-aos][data-aos][data-aos-delay="1800"],
body[data-aos-delay="1800"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1800"].aos-animate,
body[data-aos-delay="1800"] [data-aos].aos-animate {
    transition-delay: 1.8s
}

[data-aos][data-aos][data-aos-duration="1850"],
body[data-aos-duration="1850"] [data-aos] {
    transition-duration: 1.85s
}

[data-aos][data-aos][data-aos-delay="1850"],
body[data-aos-delay="1850"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1850"].aos-animate,
body[data-aos-delay="1850"] [data-aos].aos-animate {
    transition-delay: 1.85s
}

[data-aos][data-aos][data-aos-duration="1900"],
body[data-aos-duration="1900"] [data-aos] {
    transition-duration: 1.9s
}

[data-aos][data-aos][data-aos-delay="1900"],
body[data-aos-delay="1900"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1900"].aos-animate,
body[data-aos-delay="1900"] [data-aos].aos-animate {
    transition-delay: 1.9s
}

[data-aos][data-aos][data-aos-duration="1950"],
body[data-aos-duration="1950"] [data-aos] {
    transition-duration: 1.95s
}

[data-aos][data-aos][data-aos-delay="1950"],
body[data-aos-delay="1950"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="1950"].aos-animate,
body[data-aos-delay="1950"] [data-aos].aos-animate {
    transition-delay: 1.95s
}

[data-aos][data-aos][data-aos-duration="2000"],
body[data-aos-duration="2000"] [data-aos] {
    transition-duration: 2s
}

[data-aos][data-aos][data-aos-delay="2000"],
body[data-aos-delay="2000"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2000"].aos-animate,
body[data-aos-delay="2000"] [data-aos].aos-animate {
    transition-delay: 2s
}

[data-aos][data-aos][data-aos-duration="2050"],
body[data-aos-duration="2050"] [data-aos] {
    transition-duration: 2.05s
}

[data-aos][data-aos][data-aos-delay="2050"],
body[data-aos-delay="2050"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2050"].aos-animate,
body[data-aos-delay="2050"] [data-aos].aos-animate {
    transition-delay: 2.05s
}

[data-aos][data-aos][data-aos-duration="2100"],
body[data-aos-duration="2100"] [data-aos] {
    transition-duration: 2.1s
}

[data-aos][data-aos][data-aos-delay="2100"],
body[data-aos-delay="2100"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2100"].aos-animate,
body[data-aos-delay="2100"] [data-aos].aos-animate {
    transition-delay: 2.1s
}

[data-aos][data-aos][data-aos-duration="2150"],
body[data-aos-duration="2150"] [data-aos] {
    transition-duration: 2.15s
}

[data-aos][data-aos][data-aos-delay="2150"],
body[data-aos-delay="2150"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2150"].aos-animate,
body[data-aos-delay="2150"] [data-aos].aos-animate {
    transition-delay: 2.15s
}

[data-aos][data-aos][data-aos-duration="2200"],
body[data-aos-duration="2200"] [data-aos] {
    transition-duration: 2.2s
}

[data-aos][data-aos][data-aos-delay="2200"],
body[data-aos-delay="2200"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2200"].aos-animate,
body[data-aos-delay="2200"] [data-aos].aos-animate {
    transition-delay: 2.2s
}

[data-aos][data-aos][data-aos-duration="2250"],
body[data-aos-duration="2250"] [data-aos] {
    transition-duration: 2.25s
}

[data-aos][data-aos][data-aos-delay="2250"],
body[data-aos-delay="2250"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2250"].aos-animate,
body[data-aos-delay="2250"] [data-aos].aos-animate {
    transition-delay: 2.25s
}

[data-aos][data-aos][data-aos-duration="2300"],
body[data-aos-duration="2300"] [data-aos] {
    transition-duration: 2.3s
}

[data-aos][data-aos][data-aos-delay="2300"],
body[data-aos-delay="2300"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2300"].aos-animate,
body[data-aos-delay="2300"] [data-aos].aos-animate {
    transition-delay: 2.3s
}

[data-aos][data-aos][data-aos-duration="2350"],
body[data-aos-duration="2350"] [data-aos] {
    transition-duration: 2.35s
}

[data-aos][data-aos][data-aos-delay="2350"],
body[data-aos-delay="2350"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2350"].aos-animate,
body[data-aos-delay="2350"] [data-aos].aos-animate {
    transition-delay: 2.35s
}

[data-aos][data-aos][data-aos-duration="2400"],
body[data-aos-duration="2400"] [data-aos] {
    transition-duration: 2.4s
}

[data-aos][data-aos][data-aos-delay="2400"],
body[data-aos-delay="2400"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2400"].aos-animate,
body[data-aos-delay="2400"] [data-aos].aos-animate {
    transition-delay: 2.4s
}

[data-aos][data-aos][data-aos-duration="2450"],
body[data-aos-duration="2450"] [data-aos] {
    transition-duration: 2.45s
}

[data-aos][data-aos][data-aos-delay="2450"],
body[data-aos-delay="2450"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2450"].aos-animate,
body[data-aos-delay="2450"] [data-aos].aos-animate {
    transition-delay: 2.45s
}

[data-aos][data-aos][data-aos-duration="2500"],
body[data-aos-duration="2500"] [data-aos] {
    transition-duration: 2.5s
}

[data-aos][data-aos][data-aos-delay="2500"],
body[data-aos-delay="2500"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2500"].aos-animate,
body[data-aos-delay="2500"] [data-aos].aos-animate {
    transition-delay: 2.5s
}

[data-aos][data-aos][data-aos-duration="2550"],
body[data-aos-duration="2550"] [data-aos] {
    transition-duration: 2.55s
}

[data-aos][data-aos][data-aos-delay="2550"],
body[data-aos-delay="2550"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2550"].aos-animate,
body[data-aos-delay="2550"] [data-aos].aos-animate {
    transition-delay: 2.55s
}

[data-aos][data-aos][data-aos-duration="2600"],
body[data-aos-duration="2600"] [data-aos] {
    transition-duration: 2.6s
}

[data-aos][data-aos][data-aos-delay="2600"],
body[data-aos-delay="2600"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2600"].aos-animate,
body[data-aos-delay="2600"] [data-aos].aos-animate {
    transition-delay: 2.6s
}

[data-aos][data-aos][data-aos-duration="2650"],
body[data-aos-duration="2650"] [data-aos] {
    transition-duration: 2.65s
}

[data-aos][data-aos][data-aos-delay="2650"],
body[data-aos-delay="2650"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2650"].aos-animate,
body[data-aos-delay="2650"] [data-aos].aos-animate {
    transition-delay: 2.65s
}

[data-aos][data-aos][data-aos-duration="2700"],
body[data-aos-duration="2700"] [data-aos] {
    transition-duration: 2.7s
}

[data-aos][data-aos][data-aos-delay="2700"],
body[data-aos-delay="2700"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2700"].aos-animate,
body[data-aos-delay="2700"] [data-aos].aos-animate {
    transition-delay: 2.7s
}

[data-aos][data-aos][data-aos-duration="2750"],
body[data-aos-duration="2750"] [data-aos] {
    transition-duration: 2.75s
}

[data-aos][data-aos][data-aos-delay="2750"],
body[data-aos-delay="2750"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2750"].aos-animate,
body[data-aos-delay="2750"] [data-aos].aos-animate {
    transition-delay: 2.75s
}

[data-aos][data-aos][data-aos-duration="2800"],
body[data-aos-duration="2800"] [data-aos] {
    transition-duration: 2.8s
}

[data-aos][data-aos][data-aos-delay="2800"],
body[data-aos-delay="2800"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2800"].aos-animate,
body[data-aos-delay="2800"] [data-aos].aos-animate {
    transition-delay: 2.8s
}

[data-aos][data-aos][data-aos-duration="2850"],
body[data-aos-duration="2850"] [data-aos] {
    transition-duration: 2.85s
}

[data-aos][data-aos][data-aos-delay="2850"],
body[data-aos-delay="2850"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2850"].aos-animate,
body[data-aos-delay="2850"] [data-aos].aos-animate {
    transition-delay: 2.85s
}

[data-aos][data-aos][data-aos-duration="2900"],
body[data-aos-duration="2900"] [data-aos] {
    transition-duration: 2.9s
}

[data-aos][data-aos][data-aos-delay="2900"],
body[data-aos-delay="2900"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2900"].aos-animate,
body[data-aos-delay="2900"] [data-aos].aos-animate {
    transition-delay: 2.9s
}

[data-aos][data-aos][data-aos-duration="2950"],
body[data-aos-duration="2950"] [data-aos] {
    transition-duration: 2.95s
}

[data-aos][data-aos][data-aos-delay="2950"],
body[data-aos-delay="2950"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="2950"].aos-animate,
body[data-aos-delay="2950"] [data-aos].aos-animate {
    transition-delay: 2.95s
}

[data-aos][data-aos][data-aos-duration="3000"],
body[data-aos-duration="3000"] [data-aos] {
    transition-duration: 3s
}

[data-aos][data-aos][data-aos-delay="3000"],
body[data-aos-delay="3000"] [data-aos] {
    transition-delay: 0
}

[data-aos][data-aos][data-aos-delay="3000"].aos-animate,
body[data-aos-delay="3000"] [data-aos].aos-animate {
    transition-delay: 3s
}

[data-aos][data-aos][data-aos-easing=linear],
body[data-aos-easing=linear] [data-aos] {
    transition-timing-function: cubic-bezier(.25, .25, .75, .75)
}

[data-aos][data-aos][data-aos-easing=ease],
body[data-aos-easing=ease] [data-aos] {
    transition-timing-function: ease
}

[data-aos][data-aos][data-aos-easing=ease-in],
body[data-aos-easing=ease-in] [data-aos] {
    transition-timing-function: ease-in
}

[data-aos][data-aos][data-aos-easing=ease-out],
body[data-aos-easing=ease-out] [data-aos] {
    transition-timing-function: ease-out
}

[data-aos][data-aos][data-aos-easing=ease-in-out],
body[data-aos-easing=ease-in-out] [data-aos] {
    transition-timing-function: ease-in-out
}

[data-aos][data-aos][data-aos-easing=ease-in-back],
body[data-aos-easing=ease-in-back] [data-aos] {
    transition-timing-function: cubic-bezier(.6, -.28, .735, .045)
}

[data-aos][data-aos][data-aos-easing=ease-out-back],
body[data-aos-easing=ease-out-back] [data-aos] {
    transition-timing-function: cubic-bezier(.175, .885, .32, 1.275)
}

[data-aos][data-aos][data-aos-easing=ease-in-out-back],
body[data-aos-easing=ease-in-out-back] [data-aos] {
    transition-timing-function: cubic-bezier(.68, -.55, .265, 1.55)
}

[data-aos][data-aos][data-aos-easing=ease-in-sine],
body[data-aos-easing=ease-in-sine] [data-aos] {
    transition-timing-function: cubic-bezier(.47, 0, .745, .715)
}

[data-aos][data-aos][data-aos-easing=ease-out-sine],
body[data-aos-easing=ease-out-sine] [data-aos] {
    transition-timing-function: cubic-bezier(.39, .575, .565, 1)
}

[data-aos][data-aos][data-aos-easing=ease-in-out-sine],
body[data-aos-easing=ease-in-out-sine] [data-aos] {
    transition-timing-function: cubic-bezier(.445, .05, .55, .95)
}

[data-aos][data-aos][data-aos-easing=ease-in-quad],
body[data-aos-easing=ease-in-quad] [data-aos] {
    transition-timing-function: cubic-bezier(.55, .085, .68, .53)
}

[data-aos][data-aos][data-aos-easing=ease-out-quad],
body[data-aos-easing=ease-out-quad] [data-aos] {
    transition-timing-function: cubic-bezier(.25, .46, .45, .94)
}

[data-aos][data-aos][data-aos-easing=ease-in-out-quad],
body[data-aos-easing=ease-in-out-quad] [data-aos] {
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

[data-aos][data-aos][data-aos-easing=ease-in-cubic],
body[data-aos-easing=ease-in-cubic] [data-aos] {
    transition-timing-function: cubic-bezier(.55, .085, .68, .53)
}

[data-aos][data-aos][data-aos-easing=ease-out-cubic],
body[data-aos-easing=ease-out-cubic] [data-aos] {
    transition-timing-function: cubic-bezier(.25, .46, .45, .94)
}

[data-aos][data-aos][data-aos-easing=ease-in-out-cubic],
body[data-aos-easing=ease-in-out-cubic] [data-aos] {
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

[data-aos][data-aos][data-aos-easing=ease-in-quart],
body[data-aos-easing=ease-in-quart] [data-aos] {
    transition-timing-function: cubic-bezier(.55, .085, .68, .53)
}

[data-aos][data-aos][data-aos-easing=ease-out-quart],
body[data-aos-easing=ease-out-quart] [data-aos] {
    transition-timing-function: cubic-bezier(.25, .46, .45, .94)
}

[data-aos][data-aos][data-aos-easing=ease-in-out-quart],
body[data-aos-easing=ease-in-out-quart] [data-aos] {
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

[data-aos^=fade][data-aos^=fade] {
    opacity: 0;
    transition-property: opacity, transform
}

[data-aos^=fade][data-aos^=fade].aos-animate {
    opacity: 1;
    transform: translateZ(0)
}

[data-aos=fade-up] {
    transform: translate3d(0, 100px, 0)
}

[data-aos=fade-down] {
    transform: translate3d(0, -100px, 0)
}

[data-aos=fade-right] {
    transform: translate3d(-100px, 0, 0)
}

[data-aos=fade-left] {
    transform: translate3d(100px, 0, 0)
}

[data-aos=fade-up-right] {
    transform: translate3d(-100px, 100px, 0)
}

[data-aos=fade-up-left] {
    transform: translate3d(100px, 100px, 0)
}

[data-aos=fade-down-right] {
    transform: translate3d(-100px, -100px, 0)
}

[data-aos=fade-down-left] {
    transform: translate3d(100px, -100px, 0)
}

[data-aos^=zoom][data-aos^=zoom] {
    opacity: 0;
    transition-property: opacity, transform
}

[data-aos^=zoom][data-aos^=zoom].aos-animate {
    opacity: 1;
    transform: translateZ(0) scale(1)
}

[data-aos=zoom-in] {
    transform: scale(.6)
}

[data-aos=zoom-in-up] {
    transform: translate3d(0, 100px, 0) scale(.6)
}

[data-aos=zoom-in-down] {
    transform: translate3d(0, -100px, 0) scale(.6)
}

[data-aos=zoom-in-right] {
    transform: translate3d(-100px, 0, 0) scale(.6)
}

[data-aos=zoom-in-left] {
    transform: translate3d(100px, 0, 0) scale(.6)
}

[data-aos=zoom-out] {
    transform: scale(1.2)
}

[data-aos=zoom-out-up] {
    transform: translate3d(0, 100px, 0) scale(1.2)
}

[data-aos=zoom-out-down] {
    transform: translate3d(0, -100px, 0) scale(1.2)
}

[data-aos=zoom-out-right] {
    transform: translate3d(-100px, 0, 0) scale(1.2)
}

[data-aos=zoom-out-left] {
    transform: translate3d(100px, 0, 0) scale(1.2)
}

[data-aos^=slide][data-aos^=slide] {
    transition-property: transform
}

[data-aos^=slide][data-aos^=slide].aos-animate {
    transform: translateZ(0)
}

[data-aos=slide-up] {
    transform: translate3d(0, 100%, 0)
}

[data-aos=slide-down] {
    transform: translate3d(0, -100%, 0)
}

[data-aos=slide-right] {
    transform: translate3d(-100%, 0, 0)
}

[data-aos=slide-left] {
    transform: translate3d(100%, 0, 0)
}

[data-aos^=flip][data-aos^=flip] {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transition-property: transform
}

[data-aos=flip-left] {
    transform: perspective(2500px) rotateY(-100deg)
}

[data-aos=flip-left].aos-animate {
    transform: perspective(2500px) rotateY(0)
}

[data-aos=flip-right] {
    transform: perspective(2500px) rotateY(100deg)
}

[data-aos=flip-right].aos-animate {
    transform: perspective(2500px) rotateY(0)
}

[data-aos=flip-up] {
    transform: perspective(2500px) rotateX(-100deg)
}

[data-aos=flip-up].aos-animate {
    transform: perspective(2500px) rotateX(0)
}

[data-aos=flip-down] {
    transform: perspective(2500px) rotateX(100deg)
}

[data-aos=flip-down].aos-animate {
    transform: perspective(2500px) rotateX(0)
}

[data-quiz-swiper] {
    overflow: hidden;
    position: relative
}

.quiz-swiper-wrapper {
    display: flex;
    height: 100%;
    position: relative;
    width: 100%
}

.quiz-swiper-slide {
    flex-shrink: 0;
    width: 100%
}

[data-quiz-card] {
    perspective: 2400px
}

[data-quiz-card-face] {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    transition: transform .5s cubic-bezier(.77, 0, .175, 1)
}

[data-quiz-card-face=front] {
    background-image: linear-gradient(to right bottom, rgba(0, 0, 0, .625), transparent)
}

[data-quiz-card-face=back] {
    background-image: linear-gradient(to left bottom, rgba(0, 0, 0, .75), transparent);
    transform: rotateY(180deg)
}

.flip-on-swipe [data-quiz-card-face=front] {
    transform: rotateY(-180deg)
}

.flip-on-swipe [data-quiz-card-face=back] {
    transform: rotateY(0deg)
}

[data-quiz-card-face=front]:after {
    border: 1px solid #000;
    border-radius: inherit;
    content: "";
    display: block;
    inset: -1px;
    mask-image: linear-gradient(180deg, #fff, transparent);
    -webkit-mask-image: linear-gradient(180deg, transparent, rgba(0, 0, 0, .5));
    position: absolute
}

[data-quiz-card]:focus [data-quiz-card-face=front],
[data-quiz-card]:hover [data-quiz-card-face=front] {
    transform: rotateY(-180deg)
}

[data-quiz-card]:focus [data-quiz-card-face=back],
[data-quiz-card]:hover [data-quiz-card-face=back] {
    transform: rotateY(0deg)
}

@keyframes auto-fade-in {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.auto-fade-in {
    animation: auto-fade-in .5s linear
}

@keyframes auto-fade-out {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

.auto-fade-out {
    animation: auto-fade-in .5s linear
}

.quiz-scrollbar .swiper-scrollbar-drag {
    background: #27ae60 !important
}

:root {
    --swiper-navigation-size: 14px !important
}

.spacing-container-phase-4 {
    margin-bottom: 2rem;
    margin-top: 2rem;
    padding-left: 2rem;
    padding-right: 2rem;
    width: 100%
}

@media (min-width:768px) {
    .spacing-container-phase-4 {
        margin-bottom: 3rem;
        margin-top: 3rem;
        padding-left: 3rem;
        padding-right: 3rem
    }
}

@media (min-width:994px) {
    .spacing-container-phase-4 {
        margin-bottom: 5rem;
        margin-top: 5rem;
        padding-left: 5rem;
        padding-right: 5rem
    }
}

.spacing-container-phase-4>* {
    margin-left: auto;
    margin-right: auto;
    max-width: 1774px
}

#modal {
    display: none
}

#modal.is-open {
    display: block
}

.card-carousel-pagination-custom .swiper-pagination-bullet {
    border-radius: 0;
    box-sizing: content-box;
    height: 3px;
    width: 84px
}

.swiper-button-video-slider-next,
.swiper-button-video-slider-prev {
    background: #000;
    border-radius: 9999px;
    height: 49px !important;
    width: 49px !important
}

.swiper-button-video-slider-next {
    right: 5% !important;
    top: 45% !important
}

@media only screen and (max-width:995px) {
    .swiper-button-video-slider-next {
        display: none !important
    }
}

.video-carousel-pagination-custom .swiper-pagination-bullet {
    background: #000;
    border-radius: 0;
    box-sizing: content-box;
    height: 3px;
    width: 84px
}

.card-carousel-custom-pagination-black .swiper-pagination-bullet {
    background: grey;
    border-radius: 0;
    box-sizing: content-box;
    height: 3px;
    width: 84px
}

.card-carousel-custom-pagination-black .swiper-pagination-bullet:hover,
.video-carousel-pagination-custom .swiper-pagination-bullet:hover {
    background: grey !important;
    opacity: .8 !important
}

.card-carousel-custom-pagination-black {
    margin-top: 2rem
}

.card-carousel-custom-pagination-black .swiper-pagination-bullet.swiper-pagination-bullet-active,
.video-carousel-custom-pagination-black .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: #000
}

.swiper-button-army-selector:after {
    display: none
}

.swiper-button-next-armies,
.swiper-button-prev-armies {
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1 !important;
    background-color: rgb(251 202 27/var(--tw-bg-opacity));
    border-radius: 9999px;
    color: rgb(0 0 0/var(--tw-text-opacity)) !important;
    height: 2.5rem !important;
    width: 2.5rem !important
}

.tab-nav-phase-4[aria-selected=true] {
    --tw-border-opacity: 1 !important;
    border-color: rgb(251 202 27/var(--tw-border-opacity)) !important
}

.army-tabs {
    border-bottom-width: 1px;
    border-color: transparent
}

.army-tabs[aria-selected=true] {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity));
    position: relative
}

.army-tabs[aria-selected=true]:before {
    border-color: #fbca1b;
    border-width: 2px;
    bottom: -4px;
    content: " ";
    left: -1rem;
    position: absolute;
    right: -1rem
}

.army-tabs[aria-selected=true]:after {
    --tw-translate-x: -50%;
    border-color: #fbca1b transparent transparent;
    border-style: solid;
    border-width: 28px 19px 0;
    bottom: -30px;
    content: " ";
    height: 0;
    left: 50%;
    position: absolute;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    width: 0;
    z-index: 20
}

#army-selector-thumbs span {
    border-bottom: 1px solid transparent;
    display: inline-block;
    padding-bottom: 1rem
}

#army-selector-thumbs .active span {
    border-color: #fbca1b
}

.burger-menu #burger-menu {
    cursor: pointer;
    height: 12px;
    overflow: visible;
    position: relative;
    width: 20px;
    z-index: 1000
}

.burger-menu #burger-menu span,
.burger-menu #burger-menu span:after,
.burger-menu #burger-menu span:before {
    background: #fff;
    display: block;
    height: 2px;
    opacity: 1;
    position: absolute;
    transition: .3s ease-in-out
}

.burger-menu #burger-menu span:before,
.burger-menu#burger-menu span:after {
    content: ""
}

.burger-menu #burger-menu span:before {
    left: 0;
    top: -5px;
    width: 20px
}

.burger-menu #burger-menu span {
    right: 0;
    top: 8px;
    width: 20px
}

.burger-menu #burger-menu span:after {
    left: 0;
    top: 5px;
    width: 20px
}

.burger-menu #burger-menu.close span:before {
    top: 0;
    transform: rotate(90deg);
    width: 20px
}

.burger-menu #burger-menu.close span {
    top: 8px;
    transform: rotate(-45deg);
    width: 20px
}

.burger-menu #burger-menu.close span:after {
    left: 0;
    opacity: 0;
    top: 0;
    transform: rotate(90deg);
    width: 0
}

.burger-menu #menu {
    height: 0;
    left: 0;
    min-height: 100vh;
    min-width: 100%;
    opacity: 0;
    padding-top: 20px;
    position: fixed;
    text-align: center;
    top: 0;
    transition: all .3s ease-in-out;
    visibility: hidden;
    z-index: 999
}

.burger-menu #menu.overlay {
    background-color: rgba(0, 0, 0, .8);
    opacity: 1;
    padding: 150px 2rem 0;
    text-align: center;
    visibility: visible
}

.sub-menu>li {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity));
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.sub-menu>li:hover {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity))
}

@media (min-width:994px) {
    .menu .menu-item {
        padding-left: 2rem;
        padding-right: 2rem
    }
}

.menu {
    margin-left: auto;
    margin-right: auto;
    text-align: left;
    width: -moz-fit-content;
    width: fit-content
}

@media (min-width:994px) {
    .menu {
        width: 100%
    }
}

.menu .current_page_item {
    --tw-text-opacity: 1 !important;
    color: rgb(251 202 27/var(--tw-text-opacity)) !important;
    text-decoration-line: underline !important
}

.menu .menu-item.hover {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity))
}

.menu-item-has-children {
    position: relative
}

.menu-item-has-children>a:after {
    --tw-content: "";
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='17' height='9' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%23fff' d='m0 1 8.321 6.982L16.642 1' fill='none'/%3E%3C/svg%3E") no-repeat 50%;
    content: var(--tw-content);
    display: block;
    height: 10px;
    position: absolute;
    right: 0;
    top: calc(50% - 5px);
    transition: transform .25s cubic-bezier(.19, 1, .22, 1);
    width: 18px
}

.menu-item-has-children.sub-menu-is-open>a:after {
    transform: rotateX(180deg)
}

.sub-menu {
    --tw-border-opacity: 1;
    --tw-backdrop-blur: blur(4px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    background-color: rgba(0, 0, 0, .75);
    border-color: rgb(255 255 255/var(--tw-border-opacity));
    border-radius: .5rem;
    border-width: 1px;
    display: flex;
    flex-direction: column;
    font-size: 1rem;
    left: 0;
    opacity: 0;
    padding-bottom: 1.5rem !important;
    padding-top: 1.5rem !important;
    pointer-events: none;
    position: absolute;
    row-gap: .5rem;
    text-align: center;
    top: 100%;
    width: 300%
}

@media (min-width:994px) {
    .sub-menu {
        left: -38%;
        width: 200%
    }
}

.sub-menu.sub-menu-is-open {
    opacity: 1 !important;
    pointer-events: auto
}

@media only screen and (max-width:993px) {
    .sub-menu {
        --tw-border-opacity: 1;
        --tw-backdrop-blur: blur(4px);
        -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
        backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
        background-color: rgba(0, 0, 0, .75);
        border-color: rgb(255 255 255/var(--tw-border-opacity));
        border-radius: .5rem;
        border-width: 1px;
        display: flex;
        flex-direction: column;
        margin-top: 20px;
        opacity: 0;
        padding: 1rem !important;
        position: absolute;
        row-gap: 1rem;
        text-align: left;
        top: 100%;
        width: 100%
    }
}

.vjs-dock-text {
    display: none
}

#modal-content video-js {
    border-radius: .75rem
}

.vjs-custom.video-js {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity))
}

.vjs-custom .vjs-big-play-button {
    --tw-border-opacity: 1;
    border-color: rgb(251 202 27/var(--tw-border-opacity))
}

.vjs-custom .vjs-play-progress,
.vjs-custom .vjs-slider-bar,
.vjs-custom .vjs-volume-level {
    --tw-bg-opacity: 1;
    background-color: rgb(251 202 27/var(--tw-bg-opacity))
}

.vjs-custom .vjs-menu .vjs-selected {
    --tw-bg-opacity: 1 !important;
    --tw-text-opacity: 1 !important;
    background-color: rgb(251 202 27/var(--tw-bg-opacity)) !important;
    color: rgb(255 255 255/var(--tw-text-opacity)) !important
}

.video-js video {
    border-radius: .75rem
}

div[data-inject="1"] {
    display: none !important
}

div[data-inject="1"]:first-of-type {
    display: block !important
}

.army-selector-gradient {
    background: linear-gradient(180deg, transparent, rgba(0, 0, 0, .5) 20%, #000 42%)
}

.army-model-swiper .swiper-slide img {
    max-width: none;
    opacity: .5;
    transition: all;
    transition-duration: .3s
}

.army-model-swiper .swiper-slide-active img {
    opacity: 1
}

.faction-tabs {
    -ms-overflow-style: none;
    margin-left: -2rem;
    margin-right: -2rem;
    overflow: scroll;
    position: relative;
    scrollbar-width: none
}

@media (min-width:768px) {
    .faction-tabs {
        margin-left: -3rem;
        margin-right: -3rem
    }
}

@media (min-width:994px) {
    .faction-tabs {
        margin-left: 0;
        margin-right: 0
    }
}

.faction-tabs::-webkit-scrollbar {
    height: 0;
    width: 0
}

.faction-tabs:after {
    background: #fff;
    bottom: 0;
    content: "";
    left: 0;
    position: absolute;
    right: 0
}

.react-tooltip-arrow {
    display: none
}

.leaflet-container {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(245 245 245/var(--tw-bg-opacity)) !important
}

.map-lead p:first-of-type:first-letter {
    float: left;
    font-family: 'Alexandria', sans-serif;
    font-size: 52px;
    line-height: 1;
    padding: 6px 10px 0 0
}

.text-and-diorama p {
    line-height: 1.75rem !important
}

/*! tailwindcss v3.2.4 | MIT License | https://tailwindcss.com*/
*,
:after,
:before {
    border: 0 solid #e5e7eb;
    box-sizing: border-box
}

:after,
:before {
    --tw-content: ""
}

html {
    -webkit-text-size-adjust: 100%;
    font-feature-settings: normal;
    font-family: Alexandria, Cairo, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
    line-height: 1.5;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4
}

body {
    line-height: inherit;
    margin: 0
}

hr {
    border-top-width: 1px;
    color: inherit;
    height: 0
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
pre,
samp {
    font-family: 'Alexandria', sans-serif;
    font-size: 1em
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    border-collapse: collapse;
    border-color: inherit;
    text-indent: 0
}

button,
input,
optgroup,
select,
textarea {
    color: inherit;
    font-family: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0
}

button,
select {
    text-transform: none
}

[type=button],
[type=reset],
[type=submit],
button {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
    margin: 0
}

fieldset {
    margin: 0
}

fieldset,
legend {
    padding: 0
}

menu,
ol,
ul {
    list-style: none;
    margin: 0;
    padding: 0
}

textarea {
    resize: vertical
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #9ca3af;
    opacity: 1
}

input::placeholder,
textarea::placeholder {
    color: #9ca3af;
    opacity: 1
}

[role=button],
button {
    cursor: pointer
}

:disabled {
    cursor: default
}

audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
    display: block;
    vertical-align: middle
}

img,
video {
    height: auto;
    max-width: 100%
}

[hidden] {
    display: none
}

[multiple],
[type=date],
[type=datetime-local],
[type=email],
[type=month],
[type=number],
[type=password],
[type=search],
[type=tel],
[type=text],
[type=time],
[type=url],
[type=week],
select,
textarea {
    --tw-shadow: 0 0 #0000;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border-color: #6b7280;
    border-radius: 0;
    border-width: 1px;
    font-size: 1rem;
    line-height: 1.5rem;
    padding: .5rem .75rem
}

[multiple]:focus,
[type=date]:focus,
[type=datetime-local]:focus,
[type=email]:focus,
[type=month]:focus,
[type=number]:focus,
[type=password]:focus,
[type=search]:focus,
[type=tel]:focus,
[type=text]:focus,
[type=time]:focus,
[type=url]:focus,
[type=week]:focus,
select:focus,
textarea:focus {
    --tw-ring-inset: var(--tw-empty,
            /*!*/
            /*!*/
        );
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    border-color: #2563eb;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    outline: 2px solid transparent;
    outline-offset: 2px
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #6b7280;
    opacity: 1
}

input::placeholder,
textarea::placeholder {
    color: #6b7280;
    opacity: 1
}

::-webkit-datetime-edit-fields-wrapper {
    padding: 0
}

::-webkit-date-and-time-value {
    min-height: 1.5em
}

::-webkit-datetime-edit,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-meridiem-field,
::-webkit-datetime-edit-millisecond-field,
::-webkit-datetime-edit-minute-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-second-field,
::-webkit-datetime-edit-year-field {
    padding-bottom: 0;
    padding-top: 0
}

select {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3E%3C/svg%3E");
    background-position: right .5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact
}

[multiple] {
    background-image: none;
    background-position: 0 0;
    background-repeat: unset;
    background-size: initial;
    padding-right: .75rem;
    -webkit-print-color-adjust: unset;
    print-color-adjust: unset
}

[type=checkbox],
[type=radio] {
    --tw-shadow: 0 0 #0000;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    background-origin: border-box;
    border-color: #6b7280;
    border-width: 1px;
    color: #2563eb;
    display: inline-block;
    flex-shrink: 0;
    height: 1rem;
    padding: 0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle;
    width: 1rem
}

[type=checkbox] {
    border-radius: 0
}

[type=radio] {
    border-radius: 100%
}

[type=checkbox]:focus,
[type=radio]:focus {
    --tw-ring-inset: var(--tw-empty,
            /*!*/
            /*!*/
        );
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    outline: 2px solid transparent;
    outline-offset: 2px
}

[type=checkbox]:checked,
[type=radio]:checked {
    background-color: currentColor;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-color: transparent
}

[type=checkbox]:checked {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 16 16' fill='%23fff' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.207 4.793a1 1 0 0 1 0 1.414l-5 5a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L6.5 9.086l4.293-4.293a1 1 0 0 1 1.414 0z'/%3E%3C/svg%3E")
}

[type=radio]:checked {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 16 16' fill='%23fff' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='8' cy='8' r='3'/%3E%3C/svg%3E")
}

[type=checkbox]:checked:focus,
[type=checkbox]:checked:hover,
[type=radio]:checked:focus,
[type=radio]:checked:hover {
    background-color: currentColor;
    border-color: transparent
}

[type=checkbox]:indeterminate {
    background-color: currentColor;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3E%3Cpath stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3E%3C/svg%3E");
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-color: transparent
}

[type=checkbox]:indeterminate:focus,
[type=checkbox]:indeterminate:hover {
    background-color: currentColor;
    border-color: transparent
}

[type=file] {
    background: unset;
    border-color: inherit;
    border-radius: 0;
    border-width: 0;
    font-size: unset;
    line-height: inherit;
    padding: 0
}

[type=file]:focus {
    outline: 1px solid ButtonText;
    outline: 1px auto -webkit-focus-ring-color
}

*,
:after,
:before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia:
}

@font-face {
    font-family: Alexandria;
    src: url(../fonts/Alexandria-Bold.ttf);
    font-family: Cairo;
    src: url(../fonts/Cairo-Regular.ttf);
}

:root {
    --swiper-theme-color: #fff;
    --swiper-pagination-bullet-inactive-color: #fff;
    --swiper-navigation-color: #fff;
    --swiper-pagination-color: #fff
}

html {
    scroll-behavior: smooth
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Alexandria', sans-serif;
    line-height: 1.25;
}

p {
    font-family: 'Cairo', serif;
    line-height: 1.625 !important
}

.story-lead ul {
    list-style-type: disc;
    list-style: disc none outside
}

.story-lead ol,
.story-lead ul {
    display: block;
    margin: 1em 0;
    padding: 0 0 0 40px
}

.story-lead ol {
    list-style-type: decimal;
    list-style: decimal none outside
}

.story-lead p:first-of-type:first-letter {
    float: right;
    font-family: Cairo, serif;
    font-size: 60px;
    line-height: 1;
    padding: 10px 10px 0 0
}

.container {
    width: 100%
}

@media (min-width:360px) {
    .container {
        max-width: 360px
    }
}

@media (min-width:768px) {
    .container {
        max-width: 768px
    }
}

@media (min-width:994px) {
    .container {
        max-width: 994px
    }
}

@media (min-width:1280px) {
    .container {
        max-width: 1280px
    }
}

@media (min-width:1920px) {
    .container {
        max-width: 1920px
    }
}

@media (min-width:3000px) {
    .container {
        max-width: 3000px
    }
}

.pointer-events-none {
    pointer-events: none
}

.pointer-events-auto {
    pointer-events: auto
}

.static {
    position: static
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.inset-0 {
    bottom: 0;
    left: 0;
    right: 0;
    top: 0
}

.inset-x-0 {
    left: 0;
    right: 0
}

.top-0 {
    top: 0
}

.left-0 {
    left: 0
}

.top-6 {
    top: 1.5rem
}

.right-6 {
    right: 1.5rem
}

.-left-8 {
    left: -2rem
}

.-left-16 {
    left: -4rem
}

.-right-8 {
    right: -2rem
}

.-right-16 {
    right: -4rem
}

.-bottom-8 {
    bottom: -2rem
}

.bottom-0 {
    bottom: 0
}

.right-0 {
    right: 0
}

.bottom-12 {
    bottom: 3rem
}

.bottom-10 {
    bottom: 2.5rem
}

.top-4 {
    top: 1rem
}

.right-4 {
    right: 1rem
}

.left-1\/2 {
    left: 50%
}

.top-full {
    top: 100%
}

.-left-\[12rem\] {
    left: -12rem
}

.-top-16 {
    top: -4rem
}

.-right-\[30rem\] {
    right: -30rem
}

.right-32 {
    right: 8rem
}

.\!bottom-\[-50px\] {
    bottom: -50px !important
}

.\!bottom-\[-43px\] {
    bottom: -43px !important
}

.top-12 {
    top: 3rem
}

.right-\[100\%\] {
    right: 100%
}

.\!bottom-0 {
    bottom: 0 !important
}

.\!top-auto {
    top: auto !important
}

.left-2 {
    left: .5rem
}

.bottom-4 {
    bottom: 1rem
}

.right-2 {
    right: .5rem
}

.-bottom-\[28px\] {
    bottom: -28px
}

.-right-\[28px\] {
    right: -28px
}

.\!bottom-\[-20px\] {
    bottom: -20px !important
}

.top-20 {
    top: 5rem
}

.top-\[55px\] {
    top: 55px
}

.bottom-\[-1px\] {
    bottom: -1px
}

.-bottom-\[12vw\] {
    bottom: -12vw
}

.-top-4 {
    top: -1rem
}

.-right-3 {
    right: -.75rem
}

.-bottom-3 {
    bottom: -.75rem
}

.-left-3 {
    left: -.75rem
}

.-left-28 {
    left: -7rem
}

.-right-28 {
    right: -7rem
}

.left-20 {
    left: 5rem
}

.right-20 {
    right: 5rem
}

.left-4 {
    left: 1rem
}

.bottom-\[calc\(74px\+1rem\)\] {
    bottom: calc(74px + 1rem)
}

.bottom-\[158px\] {
    bottom: 158px
}

.bottom-full {
    bottom: 100%
}

.top-1\/2 {
    top: 50%
}

.\!right-0 {
    right: 0 !important
}

.\!left-0 {
    left: 0 !important
}

.-left-\[25\%\] {
    left: -25%
}

.-top-\[7\%\] {
    top: -7%
}

.-top-\[1px\] {
    top: -1px
}

.top-1\/3 {
    top: 33.333333%
}

.-bottom-\[10px\] {
    bottom: -10px
}

.top-\[3\.5\%\] {
    top: 3.5%
}

.right-\[5\%\] {
    right: 5%
}

.top-auto {
    top: auto
}

.top-8 {
    top: 2rem
}

.right-8 {
    right: 2rem
}

.bottom-\[1\.5rem\] {
    bottom: 1.5rem
}

.-left-5 {
    left: -1.25rem
}

.-right-5 {
    right: -1.25rem
}

.bottom-\[20px\] {
    bottom: 20px
}

.-top-\[2px\] {
    top: -2px
}

.z-20 {
    z-index: 20
}

.z-10 {
    z-index: 10
}

.z-50 {
    z-index: 50
}

.z-30 {
    z-index: 30
}

.z-\[1\] {
    z-index: 1
}

.z-0 {
    z-index: 0
}

.-z-10 {
    z-index: -10
}

.-z-\[9\] {
    z-index: -9
}

.z-40 {
    z-index: 40
}

.\!z-10 {
    z-index: 10 !important
}

.z-\[11\] {
    z-index: 11
}

.z-\[49\] {
    z-index: 49
}

.-z-\[1\] {
    z-index: -1
}

.z-\[440\] {
    z-index: 440
}

.z-\[450\] {
    z-index: 450
}

.z-\[410\] {
    z-index: 410
}

.z-\[460\] {
    z-index: 460
}

.\!z-40 {
    z-index: 40 !important
}

.order-4 {
    order: 4
}

.order-1 {
    order: 1
}

.order-3 {
    order: 3
}

.order-2 {
    order: 2
}

.order-5 {
    order: 5
}

.col-span-3 {
    grid-column: span 3/span 3
}

.col-span-2 {
    grid-column: span 2/span 2
}

.m-auto {
    margin: auto
}

.m-6 {
    margin: 1.5rem
}

.m-8 {
    margin: 2rem
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.my-6 {
    margin-bottom: 1.5rem;
    margin-top: 1.5rem
}

.-mx-2 {
    margin-left: -.5rem;
    margin-right: -.5rem
}

.mx-1 {
    margin-left: .25rem;
    margin-right: .25rem
}

.my-5 {
    margin-bottom: 1.25rem;
    margin-top: 1.25rem
}

.\!my-0 {
    margin-bottom: 0 !important;
    margin-top: 0 !important
}

.mx-5 {
    margin-left: 1.25rem;
    margin-right: 1.25rem
}

.-mx-5 {
    margin-left: -1.25rem;
    margin-right: -1.25rem
}

.mx-0 {
    margin-left: 0;
    margin-right: 0
}

.my-4 {
    margin-bottom: 1rem;
    margin-top: 1rem
}

.my-0 {
    margin-bottom: 0;
    margin-top: 0
}

.mx-12 {
    margin-left: 3rem;
    margin-right: 3rem
}

.-mx-8 {
    margin-left: -2rem;
    margin-right: -2rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-8 {
    margin-bottom: 2rem
}

.mb-7 {
    margin-bottom: 1.75rem
}

.mb-16 {
    margin-bottom: 4rem
}

.mb-3 {
    margin-bottom: .75rem
}

.mt-4 {
    margin-top: 1rem
}

.mt-10 {
    margin-top: 2.5rem
}

.mt-6 {
    margin-top: 1.5rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.mb-10 {
    margin-bottom: 2.5rem
}

.mb-\[69px\] {
    margin-bottom: 69px
}

.mb-12 {
    margin-bottom: 3rem
}

.ml-6 {
    margin-left: 1.5rem
}

.mt-\[32px\] {
    margin-top: 32px
}

.mb-\[110px\] {
    margin-bottom: 110px
}

.-ml-\[130px\] {
    margin-left: -130px
}

.\!mt-0 {
    margin-top: 0 !important
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-0 {
    margin-bottom: 0
}

.mt-8 {
    margin-top: 2rem
}

.mt-14 {
    margin-top: 3.5rem
}

.mt-20 {
    margin-top: 5rem
}

.mr-auto {
    margin-right: auto
}

.mb-24 {
    margin-bottom: 6rem
}

.mt-16 {
    margin-top: 4rem
}

.mt-12 {
    margin-top: 3rem
}

.ml-auto {
    margin-left: auto
}

.mb-14 {
    margin-bottom: 3.5rem
}

.mt-24 {
    margin-top: 6rem
}

.mt-3 {
    margin-top: .75rem
}

.ml-4 {
    margin-left: 1rem
}

.mb-1 {
    margin-bottom: .25rem
}

.mr-2 {
    margin-right: .5rem
}

.mr-4 {
    margin-right: 1rem
}

.mt-1 {
    margin-top: .25rem
}

.\!mb-0 {
    margin-bottom: 0 !important
}

.-mt-\[3px\] {
    margin-top: -3px
}

.-mt-\[28px\] {
    margin-top: -28px
}

.mt-auto {
    margin-top: auto
}

.mt-\[25px\] {
    margin-top: 25px
}

.mt-7 {
    margin-top: 1.75rem
}

.mb-\[80px\] {
    margin-bottom: 80px
}

.mb-\[22px\] {
    margin-bottom: 22px
}

.mb-\[37px\] {
    margin-bottom: 37px
}

.mb-\[53px\] {
    margin-bottom: 53px
}

.mt-\[46px\] {
    margin-top: 46px
}

.block {
    display: block
}

.inline-block {
    display: inline-block
}

.flex {
    display: flex
}

.table {
    display: table
}

.grid {
    display: grid
}

.hidden {
    display: none
}

.aspect-square {
    aspect-ratio: 1/1
}

.aspect-\[1\.46\/1\] {
    aspect-ratio: 1.46/1
}

.h-65px {
    height: 65px
}

.h-48px {
    height: 48px
}

.h-\[40px\] {
    height: 40px
}

.h-\[87\%\] {
    height: 87%
}

.h-full {
    height: 100%
}

.h-auto {
    height: auto
}

.h-\[48px\] {
    height: 48px
}

.h-20 {
    height: 5rem
}

.h-1 {
    height: .25rem
}

.h-\[75\%\] {
    height: 75%
}

.h-\[320px\] {
    height: 320px
}

.h-\[165px\] {
    height: 165px
}

.h-\[124px\] {
    height: 124px
}

.h-\[500px\] {
    height: 500px
}

.h-\[550px\] {
    height: 550px
}

.h-\[279px\] {
    height: 279px
}

.\!h-full {
    height: 100% !important
}

.h-12 {
    height: 3rem
}

.h-\[90vh\] {
    height: 90vh
}

.h-\[70\%\] {
    height: 70%
}

.h-\[292px\] {
    height: 292px
}

.\!h-\[480px\] {
    height: 480px !important
}

.h-36 {
    height: 9rem
}

.h-fit {
    height: -moz-fit-content;
    height: fit-content
}

.h-\[215px\] {
    height: 215px
}

.h-\[46px\] {
    height: 46px
}

.h-6 {
    height: 1.5rem
}

.h-\[10\.5rem\] {
    height: 10.5rem
}

.\!h-0 {
    height: 0 !important
}

.h-9 {
    height: 2.25rem
}

.h-min {
    height: -moz-min-content;
    height: min-content
}

.h-4 {
    height: 1rem
}

.h-\[333px\] {
    height: 333px
}

.h-11 {
    height: 2.75rem
}

.h-\[100svh\] {
    height: 100svh
}

.h-3\/4 {
    height: 75%
}

.h-4\/5 {
    height: 80%
}

.h-1\/4 {
    height: 25%
}

.h-1\/3 {
    height: 33.333333%
}

.h-\[70px\] {
    height: 70px
}

.h-\[75px\] {
    height: 75px
}

.h-\[37px\] {
    height: 37px
}

.h-\[95vh\] {
    height: 95vh
}

.max-h-\[100\%\] {
    max-height: 100%
}

.max-h-\[100px\] {
    max-height: 100px
}

.max-h-\[90vh\] {
    max-height: 90vh
}

.max-h-\[49px\] {
    max-height: 49px
}

.max-h-\[223px\] {
    max-height: 223px
}

.max-h-\[48px\] {
    max-height: 48px
}

.max-h-\[350px\] {
    max-height: 350px
}

.max-h-\[750px\] {
    max-height: 750px
}

.min-h-full {
    min-height: 100%
}

.min-h-\[calc\(100vh-360px\)\] {
    min-height: calc(100vh - 360px)
}

.min-h-fit {
    min-height: -moz-fit-content;
    min-height: fit-content
}

.min-h-screen {
    min-height: 100vh
}

.min-h-\[223px\] {
    min-height: 223px
}

.min-h-\[451px\] {
    min-height: 451px
}

.min-h-\[345px\] {
    min-height: 345px
}

.min-h-\[70px\] {
    min-height: 70px
}

.min-h-\[100px\] {
    min-height: 100px
}

.min-h-\[82vh\] {
    min-height: 82vh
}

.min-h-\[600px\] {
    min-height: 600px
}

.min-h-\[528px\] {
    min-height: 528px
}

.min-h-\[500px\] {
    min-height: 500px
}

.w-65px {
    width: 65px
}

.w-48px {
    width: 48px
}

.w-\[38px\] {
    width: 38px
}

.w-full {
    width: 100%
}

.w-4\/5 {
    width: 80%
}

.w-auto {
    width: auto
}

.w-1\/3 {
    width: 33.333333%
}

.w-fit {
    width: -moz-fit-content;
    width: fit-content
}

.w-\[75\%\] {
    width: 75%
}

.w-\[324px\] {
    width: 324px
}

.w-\[90\%\] {
    width: 90%
}

.w-\[260px\] {
    width: 260px
}

.w-\[320px\] {
    width: 320px
}

.w-max {
    width: -moz-max-content;
    width: max-content
}

.w-12 {
    width: 3rem
}

.w-\[210px\] {
    width: 210px
}

.w-\[calc\(100\%\+56px\)\] {
    width: calc(100% + 56px)
}

.w-\[200px\] {
    width: 200px
}

.w-\[292px\] {
    width: 292px
}

.w-1\/2 {
    width: 50%
}

.w-3\/4 {
    width: 75%
}

.w-2\/3 {
    width: 66.666667%
}

.w-\[287px\] {
    width: 287px
}

.w-\[238px\] {
    width: 238px
}

.w-\[46px\] {
    width: 46px
}

.w-6 {
    width: 1.5rem
}

.w-9 {
    width: 2.25rem
}

.w-4 {
    width: 1rem
}

.w-11 {
    width: 2.75rem
}

.w-36 {
    width: 9rem
}

.w-\[150\%\] {
    width: 150%
}

.w-24 {
    width: 6rem
}

.w-9\/12 {
    width: 75%
}

.w-\[56px\] {
    width: 56px
}

.w-\[calc\(100\%_\+_2\.55rem\)\] {
    width: calc(100% + 2.55rem)
}

.w-\[85\%\] {
    width: 85%
}

.w-screen {
    width: 100vw
}

.w-3\/5 {
    width: 60%
}

.min-w-full {
    min-width: 100%
}

.max-w-1120 {
    max-width: 1120px
}

.max-w-\[1570px\] {
    max-width: 1570px
}

.max-w-624 {
    max-width: 624px
}

.max-w-\[1100px\] {
    max-width: 1100px
}

.max-w-\[303px\] {
    max-width: 303px
}

.max-w-\[100\%\] {
    max-width: 100%
}

.max-w-\[665px\] {
    max-width: 665px
}

.max-w-\[100vw\] {
    max-width: 100vw
}

.max-w-\[90vw\] {
    max-width: 90vw
}

.max-w-\[60px\] {
    max-width: 60px
}

.max-w-\[1774px\] {
    max-width: 1774px
}

.max-w-\[644px\] {
    max-width: 644px
}

.max-w-\[508px\] {
    max-width: 508px
}

.max-w-\[138px\] {
    max-width: 138px
}

.max-w-\[50\%\] {
    max-width: 50%
}

.max-w-\[212px\] {
    max-width: 212px
}

.max-w-\[379px\] {
    max-width: 379px
}

.max-w-\[226px\] {
    max-width: 226px
}

.max-w-\[56px\] {
    max-width: 56px
}

.max-w-\[42px\] {
    max-width: 42px
}

.max-w-\[166px\] {
    max-width: 166px
}

.max-w-\[648px\] {
    max-width: 648px
}

.max-w-\[2000px\] {
    max-width: 2000px
}

.max-w-\[112px\] {
    max-width: 112px
}

.max-w-\[340px\] {
    max-width: 340px
}

.max-w-\[224px\] {
    max-width: 224px
}

.max-w-\[642px\] {
    max-width: 642px
}

.max-w-\[1920px\] {
    max-width: 1920px
}

.max-w-\[92px\] {
    max-width: 92px
}

.max-w-\[162px\] {
    max-width: 162px
}

.max-w-\[76px\] {
    max-width: 76px
}

.max-w-\[110px\] {
    max-width: 110px
}

.max-w-\[48px\] {
    max-width: 48px
}

.\!max-w-\[100\%\] {
    max-width: 100% !important
}

.max-w-\[420px\] {
    max-width: 420px
}

.max-w-\[none\] {
    max-width: none
}

.shrink-0 {
    flex-shrink: 0
}

.shrink {
    flex-shrink: 1
}

.grow {
    flex-grow: 1
}

.basis-full {
    flex-basis: 100%
}

.origin-center {
    transform-origin: center
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%
}

.-translate-x-1\/2,
.-translate-y-1\/2 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-y-1\/2 {
    --tw-translate-y: -50%
}

.translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.\!-translate-x-1\/2 {
    --tw-translate-x: -50% !important;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important
}

.rotate-180 {
    --tw-rotate: 180deg
}

.rotate-180,
.rotate-90 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-90 {
    --tw-rotate: 90deg
}

.-rotate-90 {
    --tw-rotate: -90deg
}

.-rotate-90,
.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

@keyframes bounce {

    0%,
    to {
        animation-timing-function: cubic-bezier(.8, 0, 1, 1);
        transform: translateY(-25%)
    }

    50% {
        animation-timing-function: cubic-bezier(0, 0, .2, 1);
        transform: none
    }
}

.animate-bounce {
    animation: bounce 1s infinite
}

.cursor-pointer {
    cursor: pointer
}

.cursor-not-allowed {
    cursor: not-allowed
}

.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.resize {
    resize: both
}

.list-inside {
    list-style-position: inside
}

.list-disc {
    list-style-type: disc
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr))
}

.flex-row {
    flex-direction: row
}

.flex-row-reverse {
    flex-direction: row-reverse
}

.flex-col {
    flex-direction: column
}

.flex-col-reverse {
    flex-direction: column-reverse
}

.flex-wrap {
    flex-wrap: wrap
}

.place-content-start {
    place-content: start
}

.place-items-center {
    place-items: center
}

.content-center {
    align-content: center
}

.items-start {
    align-items: flex-start
}

.items-end {
    align-items: flex-end
}

.items-center {
    align-items: center
}

.justify-start {
    justify-content: flex-start
}

.justify-end {
    justify-content: flex-end
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.gap-\[50px\] {
    gap: 50px
}

.gap-6 {
    gap: 1.5rem
}

.gap-4 {
    gap: 1rem
}

.gap-12 {
    gap: 3rem
}

.gap-8 {
    gap: 2rem
}

.gap-9 {
    gap: 2.25rem
}

.gap-3 {
    gap: .75rem
}

.gap-y-8 {
    row-gap: 2rem
}

.gap-y-6 {
    row-gap: 1.5rem
}

.gap-y-10 {
    row-gap: 2.5rem
}

.gap-x-8 {
    -moz-column-gap: 2rem;
    column-gap: 2rem
}

.gap-y-4 {
    row-gap: 1rem
}

.gap-y-16 {
    row-gap: 4rem
}

.gap-y-5 {
    row-gap: 1.25rem
}

.gap-x-11 {
    -moz-column-gap: 2.75rem;
    column-gap: 2.75rem
}

.gap-x-6 {
    -moz-column-gap: 1.5rem;
    column-gap: 1.5rem
}

.gap-x-16 {
    -moz-column-gap: 4rem;
    column-gap: 4rem
}

.space-y-8>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(2rem*var(--tw-space-y-reverse));
    margin-top: calc(2rem*(1 - var(--tw-space-y-reverse)))
}

.self-start {
    align-self: flex-start
}

.overflow-auto {
    overflow: auto
}

.overflow-hidden {
    overflow: hidden
}

.overflow-clip {
    overflow: clip
}

.\!overflow-visible {
    overflow: visible !important
}

.overflow-visible {
    overflow: visible
}

.overflow-scroll {
    overflow: scroll
}

.overflow-x-auto {
    overflow-x: auto
}

.overflow-y-auto {
    overflow-y: auto
}

.overflow-x-hidden {
    overflow-x: hidden
}

.overflow-y-visible {
    overflow-y: visible
}

.overflow-x-scroll {
    overflow-x: scroll
}

.whitespace-nowrap {
    white-space: nowrap
}

.rounded-full {
    border-radius: 9999px
}

.rounded-xl {
    border-radius: .75rem
}

.rounded-md {
    border-radius: .375rem
}

.rounded-2xl {
    border-radius: 1rem
}

.rounded-lg {
    border-radius: .5rem
}

.rounded-3xl {
    border-radius: 1.5rem
}

.\!rounded-2xl {
    border-radius: 1rem !important
}

.rounded-t-xl {
    border-top-left-radius: .75rem;
    border-top-right-radius: .75rem
}

.rounded-b-2xl {
    border-bottom-left-radius: 1rem;
    border-bottom-right-radius: 1rem
}

.rounded-b-xl {
    border-bottom-left-radius: .75rem;
    border-bottom-right-radius: .75rem
}

.rounded-t-2xl {
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem
}

.border {
    border-width: 1px
}

.border-2 {
    border-width: 2px
}

.border-l {
    border-left-width: 1px
}

.border-b {
    border-bottom-width: 1px
}

.border-b-3 {
    border-bottom-width: 3px
}

.border-t {
    border-top-width: 1px
}

.border-b-2 {
    border-bottom-width: 2px
}

.border-solid {
    border-style: solid
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity))
}

.border-\[\#595959\] {
    --tw-border-opacity: 1;
    border-color: rgb(89 89 89/var(--tw-border-opacity))
}

.border-transparent {
    border-color: transparent
}

.border-black {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity))
}

.border-\[\#E2E2E2\] {
    --tw-border-opacity: 1;
    border-color: rgb(226 226 226/var(--tw-border-opacity))
}

.\!border-black {
    --tw-border-opacity: 1 !important;
    border-color: rgb(0 0 0/var(--tw-border-opacity)) !important
}

.border-yellow {
    --tw-border-opacity: 1;
    border-color: rgb(251 202 27/var(--tw-border-opacity))
}

.border-current {
    border-color: currentColor
}

.border-\[\#949494\] {
    --tw-border-opacity: 1;
    border-color: rgb(148 148 148/var(--tw-border-opacity))
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity))
}

.bg-black\/20 {
    background-color: rgba(0, 0, 0, .2)
}

.bg-transparent {
    background-color: transparent
}

.bg-yellow {
    --tw-bg-opacity: 1;
    background-color: rgb(251 202 27/var(--tw-bg-opacity))
}

.bg-\[\#E2E2E2\] {
    --tw-bg-opacity: 1;
    background-color: rgb(226 226 226/var(--tw-bg-opacity))
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.\!bg-\[\#050505\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(5 5 5/var(--tw-bg-opacity)) !important
}

.bg-\[\#9B51E04D\] {
    background-color: #9b51e04d
}

.bg-\[\#2F80ED4D\] {
    background-color: #2f80ed4d
}

.bg-black\/70 {
    background-color: rgba(0, 0, 0, .7)
}

.bg-\[\#62A7CA\] {
    --tw-bg-opacity: 1;
    background-color: rgb(98 167 202/var(--tw-bg-opacity))
}

.bg-\[\#CA62C5\] {
    --tw-bg-opacity: 1;
    background-color: rgb(202 98 197/var(--tw-bg-opacity))
}

.bg-\[black\] {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity))
}

.bg-black\/50 {
    background-color: rgba(0, 0, 0, .5)
}

.bg-\[\#F5F5F5\] {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245/var(--tw-bg-opacity))
}

.bg-top-gradient {
    background-image: linear-gradient(0deg, hsla(0, 0%, 65%, 0) 50%, #000);
}

.bg-bottom-gradient {
    background-image: linear-gradient(180deg, hsla(0, 0%, 100%, 0) 25%, #fff);
}

.bg-gradient-to-t {
    background-image: linear-gradient(to top, var(--tw-gradient-stops))
}

.bg-video-gradient {
    background-image: linear-gradient(180deg, rgba(9, 8, 8, 0), #000);
}

.bg-global-nav {
    background-image: linear-gradient(180deg, #000, transparent);
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops))
}

.bg-gradient-to-l {
    background-image: linear-gradient(to left, var(--tw-gradient-stops))
}

.bg-info-tile {
    background-image: url(../images/info-tile-background.png)
}

.bg-logo-tile {
    background-image: url(../images/logo-tile-background.png)
}

.bg-gradient-to-b {
    background-image: linear-gradient(to bottom, var(--tw-gradient-stops))
}

.from-black {
    --tw-gradient-from: #000;
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-white {
    --tw-gradient-from: #fff;
    --tw-gradient-to: hsla(0, 0%, 100%, 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.to-transparent {
    --tw-gradient-to: transparent
}

.bg-cover {
    background-size: cover
}

.bg-contain {
    background-size: contain
}

.\!bg-cover {
    background-size: cover !important
}

.bg-center {
    background-position: 50%
}

.bg-top {
    background-position: top
}

.\!bg-center {
    background-position: 50% !important
}

.\!bg-bottom {
    background-position: bottom !important
}

.bg-bottom {
    background-position: bottom
}

.bg-no-repeat {
    background-repeat: no-repeat
}

.\!bg-no-repeat {
    background-repeat: no-repeat !important
}

.fill-current {
    fill: currentColor
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain
}

.object-cover {
    -o-object-fit: cover;
    object-fit: cover
}

.\!object-cover {
    -o-object-fit: cover !important;
    object-fit: cover !important
}

.object-bottom {
    -o-object-position: bottom;
    object-position: bottom
}

.object-center {
    -o-object-position: center;
    object-position: center
}

.p-2 {
    padding: .5rem
}

.p-6 {
    padding: 1.5rem
}

.p-\[48px\] {
    padding: 48px
}

.p-5 {
    padding: 1.25rem
}

.p-7 {
    padding: 1.75rem
}

.p-\[30px\] {
    padding: 30px
}

.p-8 {
    padding: 2rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-\[25px\] {
    padding: 25px
}

.p-\[39px\] {
    padding: 39px
}

.py-5 {
    padding-bottom: 1.25rem;
    padding-top: 1.25rem
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.py-10 {
    padding-bottom: 2.5rem;
    padding-top: 2.5rem
}

.py-3 {
    padding-bottom: .75rem;
    padding-top: .75rem
}

.py-2 {
    padding-bottom: .5rem;
    padding-top: .5rem
}

.px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem
}

.py-4 {
    padding-bottom: 1rem;
    padding-top: 1rem
}

.px-\[50px\] {
    padding-left: 50px;
    padding-right: 50px
}

.px-0 {
    padding-left: 0;
    padding-right: 0
}

.py-6 {
    padding-bottom: 1.5rem;
    padding-top: 1.5rem
}

.py-24 {
    padding-bottom: 6rem;
    padding-top: 6rem
}

.py-7 {
    padding-bottom: 1.75rem;
    padding-top: 1.75rem
}

.px-px {
    padding-left: 1px;
    padding-right: 1px
}

.py-8 {
    padding-bottom: 2rem;
    padding-top: 2rem
}

.px-\[39px\] {
    padding-left: 39px;
    padding-right: 39px
}

.pb-10 {
    padding-bottom: 2.5rem
}

.pb-\[7\%\] {
    padding-bottom: 7%
}

.\!pt-4 {
    padding-top: 1rem !important
}

.pt-10 {
    padding-top: 2.5rem
}

.pb-3 {
    padding-bottom: .75rem
}

.pb-28 {
    padding-bottom: 7rem
}

.pt-5 {
    padding-top: 1.25rem
}

.pt-8 {
    padding-top: 2rem
}

.pb-12 {
    padding-bottom: 3rem
}

.pb-6 {
    padding-bottom: 1.5rem
}

.pb-20 {
    padding-bottom: 5rem
}

.pt-36 {
    padding-top: 9rem
}

.pt-20 {
    padding-top: 5rem
}

.pt-6 {
    padding-top: 1.5rem
}

.pb-14 {
    padding-bottom: 3.5rem
}

.pb-16 {
    padding-bottom: 4rem
}

.pb-2 {
    padding-bottom: .5rem
}

.pt-9 {
    padding-top: 2.25rem
}

.pt-0 {
    padding-top: 0
}

.pb-5 {
    padding-bottom: 1.25rem
}

.pt-\[150\%\] {
    padding-top: 150%
}

.pb-\[56\.25\%\] {
    padding-bottom: 56.25%
}

.\!pb-10 {
    padding-bottom: 2.5rem !important
}

.pt-4 {
    padding-top: 1rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pb-60 {
    padding-bottom: 15rem
}

.pl-5 {
    padding-left: 1.25rem
}

.pr-5 {
    padding-right: 1.25rem
}

.\!pb-0 {
    padding-bottom: 0 !important
}

.pb-8 {
    padding-bottom: 2rem
}

.pr-4 {
    padding-right: 1rem
}

.pb-\[28px\] {
    padding-bottom: 28px
}

.pt-1 {
    padding-top: .25rem
}

.pr-0 {
    padding-right: 0
}

.pl-0 {
    padding-left: 0
}

.pt-\[250px\] {
    padding-top: 250px
}

.pb-\[39px\] {
    padding-bottom: 39px
}

.pb-\[140\%\] {
    padding-bottom: 140%
}

.pt-\[50px\] {
    padding-top: 50px
}

.pb-40 {
    padding-bottom: 10rem
}

.pt-12 {
    padding-top: 3rem
}

.pt-2 {
    padding-top: .5rem
}

.pr-16 {
    padding-right: 4rem
}

.pb-7 {
    padding-bottom: 1.75rem
}

.pb-80 {
    padding-bottom: 20rem
}

.pt-14 {
    padding-top: 3.5rem
}

.pb-\[150px\] {
    padding-bottom: 150px
}

.pl-8 {
    padding-left: 2rem
}

.pb-3\.5 {
    padding-bottom: .875rem
}

.pb-0 {
    padding-bottom: 0
}

.pb-\[29px\] {
    padding-bottom: 29px
}

.pb-1 {
    padding-bottom: .25rem
}

.pb-\[26px\] {
    padding-bottom: 26px
}

.text-left {
    text-align: left
}

.text-center {
    text-align: center
}

.text-justify {
    text-align: justify
}

.align-middle {
    vertical-align: middle
}

.font-body {
    font-family: Bitter, serif
}

.font-header {
    font-family: 'Alexandria', sans-serif;
}

.\!font-header {
    font-family: 'Alexandria', sans-serif !important;
}

.text-12 {
    font-size: .75rem
}

.text-15 {
    font-size: .9375rem
}

.text-\[32px\] {
    font-size: 32px
}

.text-\[16px\] {
    font-size: 16px
}

.text-\[40px\] {
    font-size: 40px
}

.text-\[18px\] {
    font-size: 18px
}

.text-\[36px\] {
    font-size: 36px
}

.text-14 {
    font-size: .875rem
}

.text-36 {
    font-size: 2.24rem
}

.text-20 {
    font-size: 1.25rem
}

.text-\[3\.125rem\] {
    font-size: 3.125rem
}

.text-38 {
    font-size: 2.375rem
}

.text-16 {
    font-size: 1rem
}

.text-24 {
    font-size: 1.5rem
}

.text-\[14px\] {
    font-size: 14px
}

.text-\[20px\] {
    font-size: 20px
}

.text-54 {
    font-size: 3.375rem
}

.text-\[28px\] {
    font-size: 28px
}

.text-\[10px\] {
    font-size: 10px
}

.\!text-\[14px\] {
    font-size: 14px !important
}

.text-\[56px\] {
    font-size: 56px
}

.text-\[38px\] {
    font-size: 38px
}

.text-\[52px\] {
    font-size: 52px
}

.text-\[24px\] {
    font-size: 24px
}

.\!text-24 {
    font-size: 1.5rem !important
}

.font-bold {
    font-weight: 700
}

.font-medium {
    font-weight: 500
}

.font-semibold {
    font-weight: 600
}

.\!font-semibold {
    font-weight: 600 !important
}

.\!font-medium {
    font-weight: 500 !important
}

.font-normal {
    font-weight: 400
}

.uppercase {
    text-transform: uppercase
}

.capitalize {
    text-transform: capitalize
}

.not-italic {
    font-style: normal
}

.leading-none {
    line-height: 1
}

.leading-loose {
    line-height: 2
}

.\!leading-none {
    line-height: 1 !important
}

.leading-relaxed {
    line-height: 1.625
}

.\!leading-\[2\.375rem\] {
    line-height: 2.375rem !important
}

.\!leading-tight {
    line-height: 1.25 !important
}

.\!leading-loose {
    line-height: 2 !important
}

.leading-\[105\%\] {
    line-height: 105%
}

.leading-\[2\.25\] {
    line-height: 2.25
}

.tracking-\[2\%\] {
    letter-spacing: 2%
}

.tracking-\[1\%\] {
    letter-spacing: 1%
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity))
}

.text-\[\#323638\] {
    --tw-text-opacity: 1;
    color: rgb(50 54 56/var(--tw-text-opacity))
}

.text-yellow {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity))
}

.text-\[\#333333\] {
    --tw-text-opacity: 1;
    color: rgb(51 51 51/var(--tw-text-opacity))
}

.text-\[\#FBCA1B\] {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity))
}

.text-\[\#F2F2F2\] {
    --tw-text-opacity: 1;
    color: rgb(242 242 242/var(--tw-text-opacity))
}

.text-current {
    color: currentColor
}

.text-\[\#595959\] {
    --tw-text-opacity: 1;
    color: rgb(89 89 89/var(--tw-text-opacity))
}

.text-\[\#F7F7F7\] {
    --tw-text-opacity: 1;
    color: rgb(247 247 247/var(--tw-text-opacity))
}

.opacity-70 {
    opacity: .7
}

.opacity-0 {
    opacity: 0
}

.opacity-50 {
    opacity: .5
}

.opacity-75 {
    opacity: .75
}

.opacity-100 {
    opacity: 1
}

.opacity-30 {
    opacity: .3
}

.opacity-80 {
    opacity: .8
}

.opacity-20 {
    opacity: .2
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color)
}

.shadow-sm,
.shadow-xl {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color)
}

.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.outline {
    outline-style: solid
}

.outline-1 {
    outline-width: 1px
}

.outline-black {
    outline-color: #000
}

.outline-white {
    outline-color: #fff
}

.\!outline-black {
    outline-color: #000 !important
}

.blur {
    --tw-blur: blur(8px)
}

.blur,
.blur-\[150px\] {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.blur-\[150px\] {
    --tw-blur: blur(150px)
}

.drop-shadow {
    --tw-drop-shadow: drop-shadow(0 1px 2px rgba(0, 0, 0, .1)) drop-shadow(0 1px 1px rgba(0, 0, 0, .06))
}

.drop-shadow,
.drop-shadow-2xl {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.drop-shadow-2xl {
    --tw-drop-shadow: drop-shadow(0 25px 25px rgba(0, 0, 0, .15))
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.backdrop-blur-sm {
    --tw-backdrop-blur: blur(4px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)
}

.transition-all {
    transition-duration: .15s;
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.transition-colors {
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.transition {
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.transition-opacity {
    transition-duration: .15s;
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.\!transition-all {
    transition-duration: .15s !important;
    transition-property: all !important;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1) !important
}

.transition-transform {
    transition-duration: .15s;
    transition-property: transform;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.delay-500 {
    transition-delay: .5s
}

.duration-300 {
    transition-duration: .3s
}

.duration-100 {
    transition-duration: .1s
}

.duration-1000 {
    transition-duration: 1s
}

.duration-500 {
    transition-duration: .5s
}

.duration-\[250ms\] {
    transition-duration: .25s
}

.ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.ease-linear {
    transition-timing-function: linear
}

.spacing-container {
    margin-bottom: 1.25rem;
    margin-top: 1.25rem;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    width: 100%
}

@media (min-width:768px) {
    .spacing-container {
        margin-bottom: 2rem;
        margin-top: 2rem;
        padding-left: 2rem;
        padding-right: 2rem
    }
}

@media (min-width:994px) {
    .spacing-container {
        margin-bottom: 4rem;
        margin-top: 4rem;
        padding-left: 4rem;
        padding-right: 4rem
    }
}

.spacing-container>* {
    margin-left: auto;
    margin-right: auto;
    max-width: 1774px
}

#menu-top-menu .current_page_item,
#menu-top-menu-1 .current_page_item {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity))
}

.icon-facebook svg {
    height: 20px;
    width: 10px
}

.icon-twitter svg {
    height: 20px;
    width: 20px
}

.icon-twitch svg {
    height: 16px;
    width: 16px
}

.icon-youtube svg {
    height: 14px;
    width: 20px
}

div[class*=icon-] svg {
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.icon-facebook:hover svg {
    color: #3b5998
}

.icon-twitter:hover svg {
    color: #1da1f2
}

.icon-twitch:hover svg {
    color: #6441a5
}

.icon-youtube:hover svg {
    color: red
}

.video-js video {
    -o-object-fit: cover;
    object-fit: cover
}

.swiper-slide {
    height: auto !important
}

.newsletter-swiper-button-next,
.newsletter-swiper-button-prev {
    bottom: 170px;
    top: auto !important
}

.swiper-button-disabled {
    pointer-events: all !important
}

@media screen and (min-width:768px) {

    .book-swiper-button-next:after,
    .book-swiper-button-prev:after,
    .marines-swiper-button-next:after,
    .marines-swiper-button-prev:after,
    .tyranids-swiper-button-next:after,
    .tyranids-swiper-button-prev:after {
        display: none !important
    }
}

.swiper-pagination-bullet:hover {
    background-color: #fbca1b
}

@media screen and (min-width:768px) {
    .slider-resources-wrapper .swiper-pagination-bullet {
        border-radius: 0;
        box-sizing: content-box;
        height: 3px;
        width: 84px
    }
}

.modal-swiper-thumbs {
    box-sizing: border-box;
    height: 20%;
    padding: 10px 0
}

.books-swiper-thumbs .swiper-slide,
.modal-swiper-thumbs .swiper-slide {
    height: 100%;
    opacity: .4;
    transition: opacity .3s ease-in-out
}

.books-swiper-thumbs .swiper-slide:not(.swiper-slide-thumb-active):hover,
.modal-swiper-thumbs .swiper-slide:not(.swiper-slide-thumb-active):hover {
    opacity: .6
}

.books-swiper-thumbs .swiper-slide-thumb-active,
.modal-swiper-thumbs .swiper-slide-thumb-active {
    opacity: 1
}

.modal-swiper .slide-background {
    opacity: 0;
    transition: opacity .3s ease-in-out
}

.modal-swiper .swiper-slide-active .slide-background {
    opacity: .5
}

.three-d-viewer-button {
    opacity: 0;
    pointer-events: none;
    transition: opacity .3s ease-in-out
}

.swiper-slide-active .three-d-viewer-button {
    opacity: 1;
    pointer-events: all
}

.gw-brand-banner {
    z-index: 50 !important
}

.stf__parent {
    min-width: unset !important
}

.otgs-development-site-front-end {
    display: none
}

.slider-backgrounds {
    height: 100%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1
}

.slider-backgrounds .top {
    background: linear-gradient(180deg, #000 0, transparent);
    height: 224px;
    position: absolute;
    top: 0;
    width: 100%
}

.slider-backgrounds .right {
    background: linear-gradient(270deg, #000 44.08%, transparent 99.4%);
    display: none;
    height: 100%;
    opacity: .8;
    position: absolute;
    right: 0;
    top: 0;
    width: 186px
}

.slider-backgrounds .left {
    background: linear-gradient(90deg, #000 44.08%, transparent 99.4%);
    display: none;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 617px
}

.tab-panel[hidden=hidden] {
    display: none !important
}

.tab-nav[aria-selected=true] {
    --tw-border-opacity: 1 !important;
    border-color: rgb(98 167 202/var(--tw-border-opacity)) !important
}

@media screen and (min-width:994px) {
    .combat-cards .swiper-wrapper {
        transform: translateZ(0) !important
    }
}

.combat-button-next.swiper-button-disabled,
.combat-button-prev.swiper-button-disabled {
    opacity: .4
}

.swiper-scrollbar-drag {
    background: #4f4f4f !important
}

@media screen and (min-width:1024px) and (max-width:1439px) {
    .card-button {
        bottom: 2rem;
        right: 3.5rem
    }
}

.combat-patrol-card-gradient {
    background: linear-gradient(180deg, rgba(0, 0, 0, .5), transparent)
}

.combat-patrol-section-gradient {
    background: linear-gradient(180deg, hsla(0, 0%, 100%, 0), #fff 44.27%)
}

.combat-patrol-box-gradient-left {
    background: linear-gradient(90deg, #000 30.73%, transparent)
}

.combat-patrol-box-gradient-right {
    background: linear-gradient(-90deg, #000 30.73%, transparent)
}

.combat-radial {
    background: radial-gradient(50% 50% at 50% 50%, #5280aa 0, rgba(82, 128, 170, 0) 100%)
}

@media screen and (min-width:768px) {
    .slider-backgrounds .top {
        height: 145px
    }

    .slider-backgrounds .left,
    .slider-backgrounds .right {
        display: block
    }
}

@media screen and (min-width:994px) {
    .slider-backgrounds:not(.book) .left {
        width: 650px
    }

    .slider-backgrounds .right {
        display: block
    }
}

@media screen and (min-width:1280px) {
    .slider-backgrounds:not(.book) .left {
        width: 850px
    }

    .slider-backgrounds:not(.book) .right {
        width: 250px
    }
}

@media screen and (min-width:1920px) {
    .slider-backgrounds:not(.book) .left {
        width: 1251px
    }

    .slider-backgrounds:not(.book) .right {
        width: 550px
    }
}

@media screen and (min-width:2506px) {
    .slider-backgrounds:not(.book) .left {
        width: 1751px
    }

    .slider-backgrounds:not(.book) .right {
        width: 650px
    }
}

.modal-overlay {
    align-items: center;
    background: rgba(0, 0, 0, .6);
    bottom: 0;
    display: flex;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 50
}

.modal-container {
    box-sizing: border-box;
    overflow-y: auto;
    width: 100%
}

.modal-header {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.modal-title {
    box-sizing: border-box;
    color: #00449e;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: 0;
    margin-top: 0
}

.modal-content {
    color: rgba(0, 0, 0, .8);
    line-height: 1.5;
    margin-bottom: 2rem;
    margin-top: 2rem
}

.modal-btn {
    -moz-osx-font-smoothing: grayscale;
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    background-color: #e6e6e6;
    border-radius: .25rem;
    border-style: none;
    border-width: 0;
    color: rgba(0, 0, 0, .8);
    cursor: pointer;
    font-size: .875rem;
    line-height: 1.15;
    margin: 0;
    overflow: visible;
    padding: .5rem 1rem;
    text-transform: none;
    transform: translateZ(0);
    transition: transform .25s ease-out;
    will-change: transform
}

.modal-btn:focus,
.modal-btn:hover {
    transform: scale(1.05)
}

.modal-btn-primary {
    background-color: #00449e;
    color: #fff
}

@keyframes mm-fadein {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes mm-fadeout {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes mm-slide-in {
    0% {
        transform: translateY(15%)
    }

    to {
        transform: translateY(0)
    }
}

@keyframes mm-slideout {
    0% {
        transform: translateY(0)
    }

    to {
        transform: translateY(-10%)
    }
}

.micromodal-slide {
    display: none
}

.micromodal-slide.is-open {
    display: block
}

.micromodal-slide .modal-container,
.micromodal-slide .modal-overlay {
    will-change: transform
}

.micromodal-slide[aria-hidden=false] .modal-overlay {
    animation: mmfadeIn .3s cubic-bezier(0, 0, .2, 1)
}

.micromodal-slide[aria-hidden=false] .modal-container {
    animation: mmslideIn .3s cubic-bezier(0, 0, .2, 1)
}

.micromodal-slide[aria-hidden=true] .modal-overlay {
    animation: mmfadeOut .3s cubic-bezier(0, 0, .2, 1)
}

.micromodal-slide[aria-hidden=true] .modal-container {
    animation: mmslideOut .3s cubic-bezier(0, 0, .2, 1)
}

.video-js .vjs-big-play-button {
    opacity: 0;
    visibility: hidden
}

.flip-book {
    background-size: cover;
    display: none
}

.flip-page {
    background-color: #fdfaf7;
    color: #785e3a;
    overflow: hidden
}

.flip-page .page-content {
    align-items: stretch;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    width: 100%
}

.flip-page .page-content .page-image {
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    height: 100%
}

.flip-page.--left {
    border-right: 0;
    box-shadow: inset -7px 0 30px -7px rgba(0, 0, 0, .4)
}

.flip-page.--right {
    border-left: 0;
    box-shadow: inset 7px 0 30px -7px rgba(0, 0, 0, .4)
}

.flip-page.--left:after,
.flip-page.--right:after {
    background-image: linear-gradient(270deg, rgba(0, 0, 0, .5), transparent 12.5%);
    content: "";
    display: block;
    inset: 0;
    position: absolute;
    top: -30px
}

.flip-page.--right:after {
    transform: rotate(180deg)
}

.flip-page.hard {
    background-color: #f2e8d9
}

.flip-page.page-cover {
    background-color: #fff;
    color: #785e3a
}

.world-text-gradient {
    background-color: rgba(98, 167, 202, .42);
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 34px;
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 20
}

@media screen and (min-width:768px) {
    .world-text-gradient {
        border-radius: 40px
    }
}

.world-view-gradient-container {
    background: linear-gradient(180deg, #000, rgba(0, 0, 0, .503) 25%, transparent 50%, rgba(0, 0, 0, .503) 85%, #000);
    background-repeat: no-repeat;
    background-size: cover;
    height: 100%;
    width: 100%
}

@media screen and (min-width:768px) {
    .headline-banner-gradient-container {
        background: linear-gradient(180deg, #fff 55%, hsla(0, 0%, 100%, 0));
        background-size: cover;
        height: 100%;
        width: 100%
    }
}

@media screen and (max-width:767px) {
    .headline-banner-gradient-container {
        background: linear-gradient(180deg, #fff 70%, hsla(0, 0%, 100%, 0));
        background-size: cover;
        height: 100%;
        width: 100%
    }
}

.world-swiper .swiper-pagination-bullet {
    height: 12px;
    width: 12px
}

.top-thick-underline {
    top: -20px
}

.bottom-thick-underline {
    bottom: -20px
}

.bottom-underline {
    bottom: 15%
}

.top-underline {
    top: 7.5%
}

#burger-menu {
    cursor: pointer;
    height: 27px;
    overflow: visible;
    position: relative;
    width: 27px;
    z-index: 1000
}

#burger-menu span,
#burger-menu span:after,
#burger-menu span:before {
    background: #fff;
    display: block;
    height: 2px;
    opacity: 1;
    position: absolute;
    transition: .3s ease-in-out
}

#burger-menu span:after,
#burger-menu span:before {
    content: ""
}

#burger-menu span:before {
    left: 0;
    top: -10px;
    width: 27px
}

#burger-menu span {
    right: 0;
    top: 13px;
    width: 27px
}

#burger-menu span:after {
    left: 0;
    top: 10px;
    width: 27px
}

#burger-menu.close span:before {
    top: 0;
    transform: rotate(90deg);
    width: 27px
}

#burger-menu.close span {
    top: 13px;
    transform: rotate(-45deg);
    width: 27px
}

#burger-menu.close span:after {
    left: 0;
    opacity: 0;
    top: 0;
    transform: rotate(90deg);
    width: 0
}

#menu {
    height: 0;
    left: 0;
    min-height: 100%;
    min-width: 100%;
    opacity: 0;
    padding-top: 20px;
    position: fixed;
    text-align: center;
    top: 0;
    transition: all .3s ease-in-out;
    visibility: hidden;
    z-index: 999
}

#menu.overlay {
    background: rgba(0, 0, 0, .8);
    opacity: 1;
    padding-top: 150px;
    visibility: visible
}

#menu ul {
    padding: 0
}

#menu li {
    list-style: none
}

.resources-pagination-NextStepsselector .swiper-pagination-bullet {
    background-color: #000 !important
}

[data-tab-identifier][aria-selected=true] .set-full-opacity {
    opacity: 1
}

.swiper-button-next-NextStepsselector,
.swiper-button-prev-NextStepsselector {
    color: #000 !important
}

.card-summary a {
    text-decoration: underline;
    text-decoration-color: hsla(0, 0%, 100%, .6);
    transition-duration: .15s;
    transition-property: all
}

.card-summary a:hover {
    text-decoration-color: #fff
}

.menu-phase-4-nav-bar-container {
    overflow-y: scroll
}

.first-letter\:pt-1:first-letter {
    padding-top: .25rem
}

.first-letter\:font-typographer:first-letter {
    font-family: 'Alexandria', sans-serif;
}

.after\:absolute:after {
    content: var(--tw-content);
    position: absolute
}

.after\:left-0:after {
    content: var(--tw-content);
    left: 0
}

.after\:top-0:after {
    content: var(--tw-content);
    top: 0
}

.after\:-z-\[1\]:after {
    content: var(--tw-content);
    z-index: -1
}

.after\:h-full:after {
    content: var(--tw-content);
    height: 100%
}

.after\:w-full:after {
    content: var(--tw-content);
    width: 100%
}

.after\:bg-black:after {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity));
    content: var(--tw-content)
}

.after\:content-\[\'\'\]:after {
    --tw-content: "";
    content: var(--tw-content)
}

.first\:border-none:first-child {
    border-style: none
}

.first\:pl-0:first-child {
    padding-left: 0
}

.hover\:border-\[\#737373\]:hover {
    --tw-border-opacity: 1;
    border-color: rgb(115 115 115/var(--tw-border-opacity))
}

.hover\:bg-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.hover\:bg-yellow_dark:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(225 177 4/var(--tw-bg-opacity))
}

.hover\:text-yellow:hover {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity))
}

.hover\:text-slate-400:hover {
    --tw-text-opacity: 1;
    color: rgb(148 163 184/var(--tw-text-opacity))
}

.hover\:text-\[\#d8d8d8\]:hover {
    --tw-text-opacity: 1;
    color: rgb(216 216 216/var(--tw-text-opacity))
}

.hover\:underline:hover {
    text-decoration-line: underline
}

.hover\:opacity-70:hover {
    opacity: .7
}

.hover\:opacity-100:hover {
    opacity: 1
}

.hover\:opacity-80:hover {
    opacity: .8
}

.hover\:opacity-75:hover {
    opacity: .75
}

.hover\:outline-2:hover {
    outline-width: 2px
}

.hover\:outline-\[\#737373\]:hover {
    outline-color: #737373
}

.focus\:border-3:focus {
    border-width: 3px
}

.focus\:border-black:focus {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity))
}

.focus\:bg-yellow_dark:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(225 177 4/var(--tw-bg-opacity))
}

.focus\:text-yellow_dark:focus {
    --tw-text-opacity: 1;
    color: rgb(225 177 4/var(--tw-text-opacity))
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:outline:focus {
    outline-style: solid
}

.focus\:outline-2:focus {
    outline-width: 2px
}

.focus\:outline-3:focus {
    outline-width: 3px
}

.focus\:outline-black:focus {
    outline-color: #000
}

.focus\:outline-white:focus {
    outline-color: #fff
}

.focus-visible\:ring:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-yellow:focus-visible {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(251 202 27/var(--tw-ring-opacity))
}

.focus-visible\:ring-opacity-75:focus-visible {
    --tw-ring-opacity: 0.75
}

.active\:bg-black\/10:active {
    background-color: rgba(0, 0, 0, .1)
}

.active\:bg-white\/10:active {
    background-color: hsla(0, 0%, 100%, .1)
}

.disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed
}

.disabled\:bg-\[\#E2E2E2\]:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(226 226 226/var(--tw-bg-opacity))
}

.disabled\:text-black\/50:disabled {
    color: rgba(0, 0, 0, .5)
}

.disabled\:text-\[\#E2E2E2\]:disabled {
    --tw-text-opacity: 1;
    color: rgb(226 226 226/var(--tw-text-opacity))
}

.disabled\:text-white:disabled {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.disabled\:text-white\/50:disabled {
    color: hsla(0, 0%, 100%, .5)
}

.group:hover .group-hover\:border-black {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity))
}

.group:hover .group-hover\:bg-yellow_dark {
    --tw-bg-opacity: 1;
    background-color: rgb(225 177 4/var(--tw-bg-opacity))
}

.group:hover .group-hover\:text-yellow {
    --tw-text-opacity: 1;
    color: rgb(251 202 27/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-\[\#215789\] {
    --tw-text-opacity: 1;
    color: rgb(33 87 137/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity))
}

.group:hover .group-hover\:opacity-100 {
    opacity: 1
}

.group:hover .group-hover\:opacity-90 {
    opacity: .9
}

.group:hover .group-hover\:opacity-75 {
    opacity: .75
}

@media (prefers-reduced-motion:reduce) {
    .motion-reduce\:animate-none {
        animation: none
    }
}

@media not all and (min-width:1920px) {
    .max-2xl\:px-0 {
        padding-left: 0;
        padding-right: 0
    }
}

@media not all and (min-width:1280px) {
    .max-xl\:px-0 {
        padding-left: 0;
        padding-right: 0
    }
}

@media not all and (min-width:994px) {
    .max-lg\:pt-8 {
        padding-top: 2rem
    }

    .max-lg\:pr-0 {
        padding-right: 0
    }

    .max-lg\:pl-0 {
        padding-left: 0
    }
}

@media not all and (min-width:768px) {
    .max-md\:relative {
        position: relative
    }
}

@media (min-width:360px) {
    .sm\:absolute {
        position: absolute
    }

    .sm\:right-5 {
        right: 1.25rem
    }

    .sm\:top-5 {
        top: 1.25rem
    }

    .sm\:mt-7\.5 {
        margin-top: 1.875rem
    }

    .sm\:mt-7 {
        margin-top: 1.75rem
    }

    .sm\:h-82px {
        height: 82px
    }

    .sm\:w-82px {
        width: 82px
    }

    .sm\:flex-2 {
        flex: 1 1 45%
    }

    .sm\:flex-col {
        flex-direction: column
    }

    .sm\:text-\[3\.75rem\] {
        font-size: 3.75rem
    }
}

@media (min-width:768px) {
    .md\:static {
        position: static
    }

    .md\:absolute {
        position: absolute
    }

    .md\:relative {
        position: relative
    }

    .md\:bottom-0 {
        bottom: 0
    }

    .md\:top-0 {
        top: 0
    }

    .md\:bottom-auto {
        bottom: auto
    }

    .md\:-top-\[60px\] {
        top: -60px
    }

    .md\:left-0 {
        left: 0
    }

    .md\:right-0 {
        right: 0
    }

    .md\:\!right-16 {
        right: 4rem !important
    }

    .md\:\!left-0 {
        left: 0 !important
    }

    .md\:-right-20 {
        right: -5rem
    }

    .md\:-top-\[72px\] {
        top: -72px
    }

    .md\:-bottom-\[81px\] {
        bottom: -81px
    }

    .md\:top-1\/2 {
        top: 50%
    }

    .md\:left-1\/2 {
        left: 50%
    }

    .md\:\!bottom-0 {
        bottom: 0 !important
    }

    .md\:top-auto {
        top: auto
    }

    .md\:z-0 {
        z-index: 0
    }

    .md\:order-3 {
        order: 3
    }

    .md\:order-2 {
        order: 2
    }

    .md\:order-1 {
        order: 1
    }

    .md\:order-4 {
        order: 4
    }

    .md\:mx-7\.5 {
        margin-left: 1.875rem;
        margin-right: 1.875rem
    }

    .md\:mx-7 {
        margin-left: 1.75rem;
        margin-right: 1.75rem
    }

    .md\:-mx-4 {
        margin-left: -1rem;
        margin-right: -1rem
    }

    .md\:my-8 {
        margin-bottom: 2rem;
        margin-top: 2rem
    }

    .md\:mx-8 {
        margin-left: 2rem;
        margin-right: 2rem
    }

    .md\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .md\:mx-0 {
        margin-left: 0;
        margin-right: 0
    }

    .md\:my-0 {
        margin-bottom: 0;
        margin-top: 0
    }

    .md\:-mx-12 {
        margin-left: -3rem;
        margin-right: -3rem
    }

    .md\:mt-0 {
        margin-top: 0
    }

    .md\:mt-24 {
        margin-top: 6rem
    }

    .md\:mb-24 {
        margin-bottom: 6rem
    }

    .md\:mt-\[100px\] {
        margin-top: 100px
    }

    .md\:mb-\[225px\] {
        margin-bottom: 225px
    }

    .md\:-ml-0 {
        margin-left: 0
    }

    .md\:mb-0 {
        margin-bottom: 0
    }

    .md\:mb-10 {
        margin-bottom: 2.5rem
    }

    .md\:mb-6 {
        margin-bottom: 1.5rem
    }

    .md\:mr-auto {
        margin-right: auto
    }

    .md\:mb-2 {
        margin-bottom: .5rem
    }

    .md\:mb-20 {
        margin-bottom: 5rem
    }

    .md\:ml-0 {
        margin-left: 0
    }

    .md\:mr-16 {
        margin-right: 4rem
    }

    .md\:mr-20 {
        margin-right: 5rem
    }

    .md\:mt-16 {
        margin-top: 4rem
    }

    .md\:mt-2 {
        margin-top: .5rem
    }

    .md\:mb-12 {
        margin-bottom: 3rem
    }

    .md\:block {
        display: block
    }

    .md\:inline-block {
        display: inline-block
    }

    .md\:flex {
        display: flex
    }

    .md\:hidden {
        display: none
    }

    .md\:h-100px {
        height: 100px
    }

    .md\:h-min {
        height: -moz-min-content;
        height: min-content
    }

    .md\:h-\[320px\] {
        height: 320px
    }

    .md\:h-\[178px\] {
        height: 178px
    }

    .md\:h-auto {
        height: auto
    }

    .md\:h-\[550px\] {
        height: 550px
    }

    .md\:h-\[600px\] {
        height: 600px
    }

    .md\:h-\[348px\] {
        height: 348px
    }

    .md\:\!h-\[550px\] {
        height: 550px !important
    }

    .md\:h-64 {
        height: 16rem
    }

    .md\:min-h-\[498px\] {
        min-height: 498px
    }

    .md\:min-h-full {
        min-height: 100%
    }

    .md\:w-100px {
        width: 100px
    }

    .md\:w-\[45\%\] {
        width: 45%
    }

    .md\:w-\[55\%\] {
        width: 55%
    }

    .md\:w-full {
        width: 100%
    }

    .md\:w-\[458px\] {
        width: 458px
    }

    .md\:w-1\/2,
    .md\:w-\[50\%\] {
        width: 50%
    }

    .md\:w-\[348px\] {
        width: 348px
    }

    .md\:w-2\/3 {
        width: 66.666667%
    }

    .md\:w-3\/4 {
        width: 75%
    }

    .md\:max-w-\[80vw\] {
        max-width: 80vw
    }

    .md\:max-w-\[273px\] {
        max-width: 273px
    }

    .md\:max-w-\[400px\] {
        max-width: 400px
    }

    .md\:max-w-\[198px\] {
        max-width: 198px
    }

    .md\:max-w-\[96px\] {
        max-width: 96px
    }

    .md\:max-w-\[500px\] {
        max-width: 500px
    }

    .md\:max-w-\[168px\] {
        max-width: 168px
    }

    .md\:-translate-x-0 {
        --tw-translate-x: -0px
    }

    .md\:-translate-x-0,
    .md\:-translate-x-1\/2 {
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .md\:-translate-x-1\/2 {
        --tw-translate-x: -50%
    }

    .md\:-translate-y-1\/2 {
        --tw-translate-y: -50%
    }

    .md\:-translate-y-1\/2,
    .md\:transform {
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .md\:flex-row {
        flex-direction: row
    }

    .md\:place-content-center {
        place-content: center
    }

    .md\:justify-center {
        justify-content: center
    }

    .md\:gap-10 {
        gap: 2.5rem
    }

    .md\:gap-4 {
        gap: 1rem
    }

    .md\:gap-y-12 {
        row-gap: 3rem
    }

    .md\:space-y-0>:not([hidden])~:not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-bottom: calc(0px*var(--tw-space-y-reverse));
        margin-top: calc(0px*(1 - var(--tw-space-y-reverse)))
    }

    .md\:bg-contain {
        background-size: contain
    }

    .md\:p-\[24px\] {
        padding: 24px
    }

    .md\:p-10 {
        padding: 2.5rem
    }

    .md\:p-8 {
        padding: 2rem
    }

    .md\:p-0 {
        padding: 0
    }

    .md\:py-12 {
        padding-bottom: 3rem;
        padding-top: 3rem
    }

    .md\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .md\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .md\:py-20 {
        padding-bottom: 5rem;
        padding-top: 5rem
    }

    .md\:py-10 {
        padding-bottom: 2.5rem;
        padding-top: 2.5rem
    }

    .md\:pb-0 {
        padding-bottom: 0
    }

    .md\:pt-10 {
        padding-top: 2.5rem
    }

    .md\:pb-16 {
        padding-bottom: 4rem
    }

    .md\:pb-24 {
        padding-bottom: 6rem
    }

    .md\:pl-8 {
        padding-left: 2rem
    }

    .md\:pr-16 {
        padding-right: 4rem
    }

    .md\:pt-28 {
        padding-top: 7rem
    }

    .md\:pt-16 {
        padding-top: 4rem
    }

    .md\:pb-10 {
        padding-bottom: 2.5rem
    }

    .md\:pt-\[90\%\] {
        padding-top: 90%
    }

    .md\:pt-24 {
        padding-top: 6rem
    }

    .md\:pb-20 {
        padding-bottom: 5rem
    }

    .md\:pt-36 {
        padding-top: 9rem
    }

    .md\:pt-6 {
        padding-top: 1.5rem
    }

    .md\:pt-12 {
        padding-top: 3rem
    }

    .md\:pb-12 {
        padding-bottom: 3rem
    }

    .md\:pt-8 {
        padding-top: 2rem
    }

    .md\:pt-20 {
        padding-top: 5rem
    }

    .md\:pr-0 {
        padding-right: 0
    }

    .md\:pr-8 {
        padding-right: 2rem
    }

    .md\:pb-60 {
        padding-bottom: 15rem
    }

    .md\:pl-12 {
        padding-left: 3rem
    }

    .md\:text-left {
        text-align: left
    }

    .md\:text-center {
        text-align: center
    }

    .md\:text-\[48px\] {
        font-size: 48px
    }

    .md\:text-\[18px\] {
        font-size: 18px
    }

    .md\:text-\[20px\] {
        font-size: 20px
    }

    .md\:text-\[40px\] {
        font-size: 40px
    }

    .md\:text-48 {
        font-size: 3rem
    }

    .md\:text-54 {
        font-size: 3.375rem
    }

    .md\:text-24 {
        font-size: 1.5rem
    }

    .md\:text-\[64px\] {
        font-size: 64px
    }

    .md\:text-20 {
        font-size: 1.25rem
    }

    .md\:text-\[55px\] {
        font-size: 55px
    }

    .md\:text-88 {
        font-size: 5.5rem
    }

    .md\:text-\[24px\] {
        font-size: 24px
    }

    .md\:text-white {
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity))
    }

    .md\:first\:pl-4:first-child {
        padding-left: 1rem
    }

    @media not all and (min-width:994px) {
        .md\:max-lg\:top-\[250px\] {
            top: 250px
        }
    }
}

@media (min-width:994px) {
    .lg\:pointer-events-none {
        pointer-events: none
    }

    .lg\:absolute {
        position: absolute
    }

    .lg\:relative {
        position: relative
    }

    .lg\:top-10 {
        top: 2.5rem
    }

    .lg\:right-10 {
        right: 2.5rem
    }

    .lg\:-top-\[5px\] {
        top: -5px
    }

    .lg\:bottom-12 {
        bottom: 3rem
    }

    .lg\:right-8 {
        right: 2rem
    }

    .lg\:top-auto {
        top: auto
    }

    .lg\:-left-\[14rem\] {
        left: -14rem
    }

    .lg\:-right-\[20rem\] {
        right: -20rem
    }

    .lg\:\!bottom-\[-75px\] {
        bottom: -75px !important
    }

    .lg\:\!bottom-\[-67px\] {
        bottom: -67px !important
    }

    .lg\:\!left-16 {
        left: 4rem !important
    }

    .lg\:left-8 {
        left: 2rem
    }

    .lg\:bottom-1\/2 {
        bottom: 50%
    }

    .lg\:\!bottom-\[-5px\] {
        bottom: -5px !important
    }

    .lg\:top-\[105px\] {
        top: 105px
    }

    .lg\:\!bottom-\[-30px\] {
        bottom: -30px !important
    }

    .lg\:-bottom-\[20vw\] {
        bottom: -20vw
    }

    .lg\:right-auto {
        right: auto
    }

    .lg\:left-auto {
        left: auto
    }

    .lg\:bottom-auto {
        bottom: auto
    }

    .lg\:top-48 {
        top: 12rem
    }

    .lg\:left-0 {
        left: 0
    }

    .lg\:-top-\[10\%\] {
        top: -10%
    }

    .lg\:top-20 {
        top: 5rem
    }

    .lg\:right-20 {
        right: 5rem
    }

    .lg\:-right-\[19px\] {
        right: 940px
    }

    .lg\:bottom-8 {
        bottom: 2rem
    }

    .lg\:bottom-0 {
        bottom: 0
    }

    .lg\:-top-5 {
        top: -1.25rem
    }

    .lg\:order-1 {
        order: 1
    }

    .lg\:order-2 {
        order: 2
    }

    .lg\:m-16 {
        margin: 4rem
    }

    .lg\:m-\[70px\] {
        margin: 70px
    }

    .lg\:my-16 {
        margin-bottom: 4rem;
        margin-top: 4rem
    }

    .lg\:mx-16 {
        margin-left: 4rem;
        margin-right: 4rem
    }

    .lg\:mx-0 {
        margin-right: 0
    }

    .lg\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .lg\:mx-8 {
        margin-left: 2rem;
        margin-right: 2rem
    }

    .lg\:my-12 {
        margin-bottom: 3rem;
        margin-top: 3rem
    }

    .lg\:ml-22\.5 {
        margin-left: 5.625rem
    }

    .lg\:mr-11\.25 {
        margin-right: 2.8125rem
    }

    .lg\:mr-11 {
        margin-right: 2.75rem
    }

    .lg\:mb-11 {
        margin-bottom: 2.75rem
    }

    .lg\:mb-\[115px\] {
        margin-bottom: 115px
    }

    .lg\:mb-\[84px\] {
        margin-bottom: 84px
    }

    .lg\:mr-0 {
        margin-right: 0
    }

    .lg\:ml-auto {
        margin-left: auto
    }

    .lg\:mb-0 {
        margin-bottom: 0
    }

    .lg\:mt-36 {
        margin-top: 9rem
    }

    .lg\:mb-4 {
        margin-bottom: 1rem
    }

    .lg\:mt-0 {
        margin-top: 0
    }

    .lg\:mr-auto {
        margin-right: auto
    }

    .lg\:mb-2 {
        margin-bottom: .5rem
    }

    .lg\:mb-10 {
        margin-bottom: 2.5rem
    }

    .lg\:mt-12 {
        margin-top: 3rem
    }

    .lg\:-mt-\[350px\] {
        margin-top: -350px
    }

    .lg\:mb-\[35px\] {
        margin-bottom: 35px
    }

    .lg\:mt-\[60px\] {
        margin-top: 60px
    }

    .lg\:mb-\[20px\] {
        margin-bottom: 20px
    }

    .lg\:mb-\[45px\] {
        margin-bottom: 45px
    }

    .lg\:mb-\[60px\] {
        margin-bottom: 60px
    }

    .lg\:mb-8 {
        margin-bottom: 2rem
    }

    .lg\:mt-14 {
        margin-top: 3.5rem
    }

    .lg\:mb-7 {
        margin-bottom: 1.75rem
    }

    .lg\:mt-\[84px\] {
        margin-top: 84px
    }

    .lg\:block {
        display: block
    }

    .lg\:flex {
        display: flex
    }

    .lg\:\!flex {
        display: flex !important
    }

    .lg\:\!grid {
        display: grid !important
    }

    .lg\:hidden {
        display: none
    }

    .lg\:h-\[650px\] {
        height: 650px
    }

    .lg\:h-\[410px\] {
        height: 410px
    }

    .lg\:h-full {
        height: 100%
    }

    .lg\:h-\[518px\] {
        height: 518px
    }

    .lg\:\!h-\[774px\] {
        height: 774px !important
    }

    .lg\:h-72 {
        height: 18rem
    }

    .lg\:h-\[1352px\] {
        height: 1352px
    }

    .lg\:\!h-\[440px\] {
        height: 440px !important
    }

    .lg\:h-14 {
        height: 3.5rem
    }

    .lg\:h-\[4\.5rem\] {
        height: 4.5rem
    }

    .lg\:h-\[110\%\] {
        height: 110%
    }

    .lg\:h-2\/5 {
        height: 40%
    }

    .lg\:h-auto {
        height: auto
    }

    .lg\:h-\[88px\] {
        height: 88px
    }

    .lg\:max-h-\[320px\] {
        max-height: 320px
    }

    .lg\:min-h-\[115px\] {
        min-height: 115px
    }

    .lg\:min-h-\[320px\] {
        min-height: 320px
    }

    .lg\:min-h-0 {
        min-height: 0
    }

    .lg\:min-h-\[628px\] {
        min-height: 628px
    }

    .lg\:min-h-screen {
        min-height: 100vh
    }

    .lg\:min-h-\[1050px\] {
        min-height: 1050px
    }

    .lg\:w-full {
        width: 100%
    }

    .lg\:w-\[35\%\] {
        width: 35%
    }

    .lg\:\!w-full {
        width: 100% !important
    }

    .lg\:w-1\/2 {
        width: 50%
    }

    .lg\:w-auto {
        width: auto
    }

    .lg\:w-1\/3 {
        width: 33.333333%
    }

    .lg\:w-2\/3 {
        width: 66.666667%
    }

    .lg\:w-\[410px\] {
        width: 410px
    }

    .lg\:w-11\/12 {
        width: 91.666667%
    }

    .lg\:w-fit {
        width: -moz-fit-content;
        width: fit-content
    }

    .lg\:w-\[219px\] {
        width: 219px
    }

    .lg\:w-\[518px\] {
        width: 518px
    }

    .lg\:w-4\/5 {
        width: 80%
    }

    .lg\:w-\[679px\] {
        width: 679px
    }

    .lg\:w-\[545px\] {
        width: 545px
    }

    .lg\:w-12 {
        width: 3rem
    }

    .lg\:w-\[279px\] {
        width: 279px
    }

    .lg\:w-14 {
        width: 3.5rem
    }

    .lg\:w-2\/6 {
        width: 33.333333%
    }

    .lg\:w-3\/5,
    .lg\:w-\[60\%\] {
        width: 60%
    }

    .lg\:w-\[75px\] {
        width: 75px
    }

    .lg\:w-\[47\%\] {
        width: 47%
    }

    .lg\:w-3\/4 {
        width: 75%
    }

    .lg\:w-1\/4 {
        width: 25%
    }

    .lg\:max-w-\[64px\] {
        max-width: 64px
    }

    .lg\:max-w-\[76px\] {
        max-width: 76px
    }

    .lg\:max-w-\[219px\] {
        max-width: 219px
    }

    .lg\:max-w-\[706px\] {
        max-width: 706px
    }

    .lg\:max-w-\[1129px\] {
        max-width: 1129px
    }

    .lg\:max-w-\[279px\] {
        max-width: 279px
    }

    .lg\:max-w-\[134px\] {
        max-width: 134px
    }

    .lg\:\!max-w-\[100\%\] {
        max-width: 100% !important
    }

    .lg\:flex-1 {
        flex: 1 1 0%
    }

    .lg\:grow {
        flex-grow: 1
    }

    .lg\:basis-\[200\%\] {
        flex-basis: 200%
    }

    .lg\:scale-\[98\%\] {
        --tw-scale-x: 98%;
        --tw-scale-y: 98%;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .lg\:flex-row {
        flex-direction: row
    }

    .lg\:flex-col {
        flex-direction: column
    }

    .lg\:items-start {
        align-items: flex-start
    }

    .lg\:items-end {
        align-items: flex-end
    }

    .lg\:items-center {
        align-items: center
    }

    .lg\:justify-start {
        justify-content: flex-start
    }

    .lg\:justify-end {
        justify-content: flex-end
    }

    .lg\:justify-center {
        justify-content: center
    }

    .lg\:justify-between {
        justify-content: space-between
    }

    .lg\:justify-evenly {
        justify-content: space-evenly
    }

    .lg\:gap-20 {
        gap: 5rem
    }

    .lg\:gap-16 {
        gap: 4rem
    }

    .lg\:gap-y-24 {
        row-gap: 6rem
    }

    .lg\:gap-x-6 {
        -moz-column-gap: 1.5rem;
        column-gap: 1.5rem
    }

    .lg\:gap-x-12 {
        -moz-column-gap: 3rem;
        column-gap: 3rem
    }

    .lg\:gap-y-0 {
        row-gap: 0
    }

    .lg\:self-end {
        align-self: flex-end
    }

    .lg\:overflow-visible {
        overflow: visible
    }

    .lg\:overflow-x-auto {
        overflow-x: auto
    }

    .lg\:rounded-2xl {
        border-radius: 1rem
    }

    .lg\:rounded-xl {
        border-radius: .75rem
    }

    .lg\:rounded-l-2xl {
        border-bottom-left-radius: 1rem;
        border-top-left-radius: 1rem
    }

    .lg\:rounded-r-none {
        border-bottom-right-radius: 0;
        border-top-right-radius: 0
    }

    .lg\:rounded-r-xl {
        border-bottom-right-radius: .75rem;
        border-top-right-radius: .75rem
    }

    .lg\:rounded-bl-xl {
        border-bottom-left-radius: .75rem
    }

    .lg\:rounded-tl-xl {
        border-top-left-radius: .75rem
    }

    .lg\:border-b {
        border-bottom-width: 1px
    }

    .lg\:border-t {
        border-top-width: 1px
    }

    .lg\:border-solid {
        border-style: solid
    }

    .lg\:border-none {
        border-style: none
    }

    .lg\:border-white {
        --tw-border-opacity: 1;
        border-color: rgb(255 255 255/var(--tw-border-opacity))
    }

    .lg\:border-b-\[\#595959\] {
        --tw-border-opacity: 1;
        border-bottom-color: rgb(89 89 89/var(--tw-border-opacity))
    }

    .lg\:bg-black\/75 {
        background-color: rgba(0, 0, 0, .75)
    }

    .lg\:p-10 {
        padding: 2.5rem
    }

    .lg\:p-12 {
        padding: 3rem
    }

    .lg\:p-16 {
        padding: 4rem
    }

    .lg\:p-6 {
        padding: 1.5rem
    }

    .lg\:p-5 {
        padding: 1.25rem
    }

    .lg\:p-14 {
        padding: 3.5rem
    }

    .lg\:p-11 {
        padding: 2.75rem
    }

    .lg\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .lg\:px-16 {
        padding-left: 4rem;
        padding-right: 4rem
    }

    .lg\:py-24 {
        padding-bottom: 6rem;
        padding-top: 6rem
    }

    .lg\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .lg\:py-16 {
        padding-bottom: 4rem;
        padding-top: 4rem
    }

    .lg\:py-5 {
        padding-bottom: 1.25rem;
        padding-top: 1.25rem
    }

    .lg\:py-2 {
        padding-bottom: .5rem;
        padding-top: .5rem
    }

    .lg\:py-6 {
        padding-bottom: 1.5rem;
        padding-top: 1.5rem
    }

    .lg\:px-20 {
        padding-left: 5rem;
        padding-right: 5rem
    }

    .lg\:py-3 {
        padding-bottom: .75rem;
        padding-top: .75rem
    }

    .lg\:py-20 {
        padding-bottom: 5rem;
        padding-top: 5rem
    }

    .lg\:px-28 {
        padding-left: 7rem;
        padding-right: 7rem
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .lg\:px-\[86px\] {
        padding-left: 86px;
        padding-right: 86px
    }

    .lg\:pb-\[6\%\] {
        padding-bottom: 6%
    }

    .lg\:pt-32 {
        padding-top: 8rem
    }

    .lg\:pb-24 {
        padding-bottom: 6rem
    }

    .lg\:pt-24 {
        padding-top: 6rem
    }

    .lg\:pb-20 {
        padding-bottom: 5rem
    }

    .lg\:pt-48 {
        padding-top: 12rem
    }

    .lg\:pt-20 {
        padding-top: 5rem
    }

    .lg\:pr-0 {
        padding-right: 0
    }

    .lg\:pr-32 {
        padding-right: 8rem
    }

    .lg\:pb-16 {
        padding-bottom: 4rem
    }

    .lg\:pt-36 {
        padding-top: 9rem
    }

    .lg\:pt-16 {
        padding-top: 4rem
    }

    .lg\:pb-\[580px\] {
        padding-bottom: 580px
    }

    .lg\:pl-16 {
        padding-left: 4rem
    }

    .lg\:pb-0 {
        padding-bottom: 0
    }

    .lg\:pl-0 {
        padding-left: 0
    }

    .lg\:pt-0 {
        padding-top: 0
    }

    .lg\:pt-8 {
        padding-top: 2rem
    }

    .lg\:pr-3 {
        padding-right: .75rem
    }

    .lg\:pl-24 {
        padding-left: 6rem
    }

    .lg\:pr-\[192px\] {
        padding-right: 192px
    }

    .lg\:pl-\[5rem\] {
        padding-left: 5rem
    }

    .lg\:pr-\[5rem\] {
        padding-right: 5rem
    }

    .lg\:pt-10 {
        padding-top: 2.5rem
    }

    .lg\:pb-9 {
        padding-bottom: 2.25rem
    }

    .lg\:pb-\[350px\] {
        padding-bottom: 350px
    }

    .lg\:pt-\[300px\] {
        padding-top: 300px
    }

    .lg\:pb-14 {
        padding-bottom: 3.5rem
    }

    .lg\:pr-16 {
        padding-right: 4rem
    }

    .lg\:pt-\[82px\] {
        padding-top: 82px
    }

    .lg\:pl-\[102px\] {
        padding-left: 102px
    }

    .lg\:pb-7 {
        padding-bottom: 1.75rem
    }

    .lg\:pl-\[90px\] {
        padding-left: 90px
    }

    .lg\:pb-8 {
        padding-bottom: 2rem
    }

    .lg\:pb-32 {
        padding-bottom: 8rem
    }

    .lg\:pt-11 {
        padding-top: 2.75rem
    }

    .lg\:pb-3 {
        padding-bottom: .75rem
    }

    .lg\:text-left {
        text-align: left
    }

    .lg\:text-center {
        text-align: center
    }

    .lg\:text-70 {
        font-size: 4.375rem
    }

    .lg\:text-88 {
        font-size: 5.5rem
    }

    .lg\:text-68 {
        font-size: 4.25rem
    }

    .lg\:text-\[66px\] {
        font-size: 66px
    }

    .lg\:text-\[55px\] {
        font-size: 55px
    }

    .lg\:text-\[20px\] {
        font-size: 20px
    }

    .lg\:text-\[60px\] {
        font-size: 60px
    }

    .lg\:text-\[30px\] {
        font-size: 30px
    }

    .lg\:text-\[40px\] {
        font-size: 40px
    }

    .lg\:text-\[16px\] {
        font-size: 16px
    }

    .lg\:text-24 {
        font-size: 1.5rem
    }

    .lg\:text-\[70px\] {
        font-size: 70px
    }

    .lg\:text-\[56px\] {
        font-size: 56px
    }

    .lg\:text-\[24px\] {
        font-size: 24px
    }

    .lg\:text-\[32px\] {
        font-size: 32px
    }

    .lg\:\!leading-\[3\.75rem\] {
        line-height: 3.75rem !important
    }

    .lg\:leading-\[35px\] {
        line-height: 35px
    }

    .lg\:opacity-50 {
        opacity: .5
    }

    .after\:lg\:top-\[350px\]:after {
        content: var(--tw-content);
        top: 350px
    }

    .after\:lg\:h-\[calc\(100\%-350px\)\]:after {
        content: var(--tw-content);
        height: calc(100% - 350px)
    }

    .lg\:hover\:scale-100:hover {
        --tw-scale-x: 1;
        --tw-scale-y: 1;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .group:hover .lg\:group-hover\:opacity-0 {
        opacity: 0
    }
}

@media (min-width:1280px) {
    .xl\:relative {
        position: relative
    }

    .xl\:-bottom-5 {
        bottom: -1.25rem
    }

    .xl\:-right-10 {
        right: -2.5rem
    }

    .xl\:right-auto {
        right: auto
    }

    .xl\:bottom-16 {
        bottom: 4rem
    }

    .xl\:left-16 {
        left: 4rem
    }

    .xl\:left-0 {
        left: 0
    }

    .xl\:right-0 {
        right: 0
    }

    .xl\:\!bottom-16 {
        bottom: 4rem !important
    }

    .xl\:\!left-0 {
        left: 0 !important
    }

    .xl\:bottom-\[40px\] {
        bottom: 40px
    }

    .xl\:-bottom-\[12vw\] {
        bottom: -12vw
    }

    .xl\:\!top-\[82\%\] {
        top: 82% !important
    }

    .xl\:\!right-\[15\%\] {
        right: 15% !important
    }

    .xl\:\!left-\[15\%\] {
        left: 15% !important
    }

    .xl\:order-2 {
        order: 2
    }

    .xl\:order-1 {
        order: 1
    }

    .xl\:order-3 {
        order: 3
    }

    .xl\:col-span-5 {
        grid-column: span 5/span 5
    }

    .xl\:col-span-3 {
        grid-column: span 3/span 3
    }

    .xl\:col-span-4 {
        grid-column: span 4/span 4
    }

    .xl\:mx-auto {
        margin-left: auto;
        margin-right: auto
    }

    .xl\:mx-0 {
        margin-left: 0;
        margin-right: 0
    }

    .xl\:mx-16 {
        margin-left: 4rem;
        margin-right: 4rem
    }

    .xl\:mr-20 {
        margin-right: 5rem
    }

    .xl\:mr-24 {
        margin-right: 6rem
    }

    .xl\:mb-0 {
        margin-bottom: 0
    }

    .xl\:mt-0 {
        margin-top: 0
    }

    .xl\:mr-auto {
        margin-right: auto
    }

    .xl\:block {
        display: block
    }

    .xl\:grid {
        display: grid
    }

    .xl\:hidden {
        display: none
    }

    .xl\:h-\[400px\] {
        height: 400px
    }

    .xl\:h-\[420px\] {
        height: 420px
    }

    .xl\:h-\[200px\] {
        height: 200px
    }

    .xl\:h-\[600px\] {
        height: 600px
    }

    .xl\:h-\[650px\] {
        height: 650px
    }

    .xl\:h-\[577px\] {
        height: 577px
    }

    .xl\:\!h-auto {
        height: auto !important
    }

    .xl\:h-\[700px\] {
        height: 700px
    }

    .xl\:h-\[14\.5rem\] {
        height: 14.5rem
    }

    .xl\:h-auto {
        height: auto
    }

    .xl\:h-\[290px\] {
        height: 290px
    }

    .xl\:min-h-\[628px\] {
        min-height: 628px
    }

    .xl\:w-\[516px\] {
        width: 516px
    }

    .xl\:w-\[577px\] {
        width: 577px
    }

    .xl\:w-\[370px\] {
        width: 370px
    }

    .xl\:w-full {
        width: 100%
    }

    .xl\:w-3\/4 {
        width: 75%
    }

    .xl\:w-\[650px\] {
        width: 650px
    }

    .xl\:w-1\/2 {
        width: 50%
    }

    .xl\:w-2\/5 {
        width: 40%
    }

    .xl\:max-w-\[35\%\] {
        max-width: 35%
    }

    .xl\:max-w-\[660px\] {
        max-width: 660px
    }

    .xl\:max-w-\[255px\] {
        max-width: 255px
    }

    .xl\:max-w-\[225px\] {
        max-width: 225px
    }

    .xl\:max-w-\[1196px\] {
        max-width: 1196px
    }

    .xl\:\!max-w-\[100\%\] {
        max-width: 100% !important
    }

    .xl\:max-w-\[540px\] {
        max-width: 540px
    }

    .xl\:basis-1\/3 {
        flex-basis: 33.333333%
    }

    .xl\:basis-2\/3 {
        flex-basis: 66.666667%
    }

    .xl\:grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr))
    }

    .xl\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .xl\:grid-cols-7 {
        grid-template-columns: repeat(7, minmax(0, 1fr))
    }

    .xl\:flex-row {
        flex-direction: row
    }

    .xl\:items-end {
        align-items: flex-end
    }

    .xl\:justify-start {
        justify-content: flex-start
    }

    .xl\:gap-\[104px\] {
        gap: 104px
    }

    .xl\:gap-8 {
        gap: 2rem
    }

    .xl\:gap-x-0 {
        -moz-column-gap: 0;
        column-gap: 0
    }

    .xl\:gap-x-3 {
        -moz-column-gap: .75rem;
        column-gap: .75rem
    }

    .xl\:overflow-visible {
        overflow: visible
    }

    .xl\:p-12 {
        padding: 3rem
    }

    .xl\:px-32 {
        padding-left: 8rem;
        padding-right: 8rem
    }

    .xl\:px-14 {
        padding-left: 3.5rem;
        padding-right: 3.5rem
    }

    .xl\:px-\[70px\] {
        padding-left: 70px;
        padding-right: 70px
    }

    .xl\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .xl\:px-10 {
        padding-left: 2.5rem;
        padding-right: 2.5rem
    }

    .xl\:pt-12 {
        padding-top: 3rem
    }

    .xl\:pb-20 {
        padding-bottom: 5rem
    }

    .xl\:pr-16 {
        padding-right: 4rem
    }

    .xl\:pt-0 {
        padding-top: 0
    }

    .xl\:pr-48 {
        padding-right: 12rem
    }

    .xl\:pl-0 {
        padding-left: 0
    }

    .xl\:pt-\[56\.25\%\] {
        padding-top: 56.25%
    }

    .xl\:pb-32 {
        padding-bottom: 8rem
    }

    .xl\:pb-0 {
        padding-bottom: 0
    }

    .xl\:pt-36 {
        padding-top: 9rem
    }

    .xl\:pr-0 {
        padding-right: 0
    }

    .xl\:\!pt-12 {
        padding-top: 3rem !important
    }

    .xl\:pt-4 {
        padding-top: 1rem
    }

    .xl\:pb-12 {
        padding-bottom: 3rem
    }

    .xl\:pb-\[580px\] {
        padding-bottom: 580px
    }

    .xl\:pb-\[86px\] {
        padding-bottom: 86px
    }

    .xl\:text-left {
        text-align: left
    }

    .xl\:text-88 {
        font-size: 5.5rem
    }

    .xl\:text-\[88px\] {
        font-size: 88px
    }

    .xl\:text-\[60px\] {
        font-size: 60px
    }

    .xl\:text-\[20px\] {
        font-size: 20px
    }

    .xl\:text-24 {
        font-size: 1.5rem
    }

    .xl\:text-54 {
        font-size: 3.375rem
    }

    .xl\:text-\[40px\] {
        font-size: 40px
    }

    .xl\:\!leading-\[5\.75rem\] {
        line-height: 5.75rem !important
    }
}

@media (min-width:1920px) {
    .\32xl\:\!right-32 {
        right: 8rem !important
    }

    .\32xl\:\!left-32 {
        left: 8rem !important
    }

    .\32xl\:right-32 {
        left: 8rem
    }

    .\32xl\:top-0 {
        top: 0
    }

    .\32xl\:right-0 {
        right: 0
    }

    .\32xl\:-bottom-36 {
        bottom: -9rem
    }

    .\32xl\:-bottom-\[7vw\] {
        bottom: -7vw
    }

    .\32xl\:left-20 {
        left: 5rem
    }

    .\32xl\:top-20 {
        top: 5rem
    }

    .\32xl\:right-20 {
        right: 5rem
    }

    .\32xl\:top-64 {
        top: 16rem
    }

    .\32xl\:left-auto {
        left: auto
    }

    .\32xl\:top-\[10\.5\%\] {
        top: 10.5%
    }

    .\32xl\:-top-10 {
        top: -2.5rem
    }

    .\32xl\:z-10 {
        z-index: 10
    }

    .\32xl\:col-span-3 {
        grid-column: span 3/span 3
    }

    .\32xl\:mx-0 {
        margin-left: 0;
        margin-right: 0
    }

    .\32xl\:mr-auto {
        margin-right: auto
    }

    .\32xl\:mt-8 {
        margin-top: 2rem
    }

    .\32xl\:mt-20 {
        margin-top: 5rem
    }

    .\32xl\:mb-0 {
        margin-bottom: 0
    }

    .\32xl\:mt-16 {
        margin-top: 4rem
    }

    .\32xl\:mt-0 {
        margin-top: 0
    }

    .\32xl\:block {
        display: block
    }

    .\32xl\:hidden {
        display: none
    }

    .\32xl\:h-\[792px\] {
        height: 792px
    }

    .\32xl\:w-1\/2 {
        width: 50%
    }

    .\32xl\:w-\[60\%\] {
        width: 60%
    }

    .\32xl\:w-\[468px\] {
        width: 468px
    }

    .\32xl\:w-\[792px\] {
        width: 792px
    }

    .\32xl\:w-full {
        width: 100%
    }

    .\32xl\:w-3\/5 {
        width: 60%
    }

    .\32xl\:w-4\/5 {
        width: 80%
    }

    .\32xl\:max-w-\[710px\] {
        max-width: 710px
    }

    .\32xl\:max-w-\[23\%\] {
        max-width: 23%
    }

    .\32xl\:max-w-\[336px\] {
        max-width: 336px
    }

    .\32xl\:max-w-\[112px\] {
        max-width: 112px
    }

    .\32xl\:max-w-\[306px\] {
        max-width: 306px
    }

    .\32xl\:flex-row {
        flex-direction: row
    }

    .\32xl\:justify-end {
        justify-content: flex-end
    }

    .\32xl\:gap-8 {
        gap: 2rem
    }

    .\32xl\:overflow-visible {
        overflow: visible
    }

    .\32xl\:p-16 {
        padding: 4rem
    }

    .\32xl\:p-10 {
        padding: 2.5rem
    }

    .\32xl\:px-16 {
        padding-left: 4rem;
        padding-right: 4rem
    }

    .\32xl\:py-36 {
        padding-bottom: 9rem;
        padding-top: 9rem
    }

    .\32xl\:px-\[80px\] {
        padding-left: 80px;
        padding-right: 80px
    }

    .\32xl\:px-24 {
        padding-left: 6rem;
        padding-right: 6rem
    }

    .\32xl\:px-14 {
        padding-left: 3.5rem;
        padding-right: 3.5rem
    }

    .\32xl\:pb-0 {
        padding-bottom: 0
    }

    .\32xl\:pb-10 {
        padding-bottom: 2.5rem
    }

    .\32xl\:pl-0 {
        padding-left: 0
    }

    .\32xl\:pt-40 {
        padding-top: 10rem
    }

    .\32xl\:pr-48 {
        padding-right: 12rem
    }

    .\32xl\:pt-16 {
        padding-top: 4rem
    }

    .\32xl\:pb-36 {
        padding-bottom: 9rem
    }

    .\32xl\:pt-0 {
        padding-top: 0
    }

    .\32xl\:pt-36 {
        padding-top: 9rem
    }

    .\32xl\:pt-\[200px\] {
        padding-top: 200px
    }

    .\32xl\:pb-\[580px\] {
        padding-bottom: 580px
    }

    .\32xl\:text-left {
        text-align: left
    }

    .\32xl\:text-\[88px\] {
        font-size: 88px
    }

    .\32xl\:\!leading-\[5\.75rem\] {
        line-height: 5.75rem !important
    }
}

@media (min-width:3000px) {
    .\33xl\:-bottom-\[5vw\] {
        bottom: -5vw
    }

    .\33xl\:min-h-\[1500px\] {
        min-height: 1500px
    }

    .\33xl\:w-\[400px\] {
        width: 400px
    }

    .\33xl\:max-w-\[455px\] {
        max-width: 455px
    }

    .\33xl\:max-w-\[350px\] {
        max-width: 350px
    }
}
.arabic-text {
    text-align: right;
    direction: rtl;
}
.alexandria-font {
    font-family: 'Alexandria', sans-serif;
}
body {
    overflow-x: hidden;
    font-family: Sans-Serif;
    margin: 0;
  }
  
  .menu-container {
    position: relative;
    display: flex;
    align-items: center;
    background: #232323;
    color: #cdcdcd;
    padding: 20px;
    z-index: 1;
    -webkit-user-select: none;
    user-select: none;
    box-sizing: border-box;
  }
  
  .menu-logo {
    line-height: 0;
    margin: 0 20px;
  }
  
  .menu-logo img {
    max-height: 40px;
    max-width: 100px;
    flex-shrink: 0;
  }
  
  .menu-container a {
    text-decoration: none;
    color: #232323;
    transition: color 0.3s ease;
  }
  
  .menu-container a:hover {
    color: #00C6A7;
  }
  
  .menu-container input {
    display: block;
    width: 35px;
    height: 25px;
    margin-left: 380px;
    margin-top: 20px;
    position: absolute;
    cursor: pointer;
    opacity: 0; /* hide this */
    z-index: 2; /* and place it over the hamburger */
    -webkit-touch-callout: none;
  }
  
  /* Burger menu */
  .menu-container span {
    display: block;
    width: 33px;
    height: 4px;
    margin-bottom: 5px;
    position: relative;
    background: #cdcdcd;
    border-radius: 3px;
    z-index: 1;
    transform-origin: 4px 0px;
    transition: transform 0.5s cubic-bezier(0.77,0.2,0.05,1.0),
                background 0.5s cubic-bezier(0.77,0.2,0.05,1.0),
                opacity 0.55s ease;
  }
  
  .menu-container span:first-child {
    transform-origin: 0% 0%;
  }
  
  .menu-container span:nth-child(3) {
    transform-origin: 0% 100%;
  }
  
  .menu-container input:checked ~ span {
    opacity: 1;
    transform: rotate(45deg) translate(3px,-1px);
    background: #232323;
  }
  
  .menu-container input:checked ~ span:nth-child(4) {
    opacity: 0;
    transform: rotate(0deg) scale(0.2, 0.2);
  }
  
  .menu-container input:checked ~ span:nth-child(3) {
    transform: rotate(-45deg) translate(-5px,11px);
  }
  
  .menu ul {
    list-style: none;
  }
  
  .menu li {
    padding: 10px 0;
    font-size: 15px;
  }
  
  /* mobile styles */
  @media only screen and (max-width: 767px) { 
    .menu-container {
      flex-direction: column;
      align-items: flex-end;
    }
    
    .menu-logo {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  
    .menu-logo img {
      max-height: 30px;
    }
  
    .menu {
      position: absolute;
      box-sizing: border-box;
      width: 300px;
      right: -300px;
      top: 0;
      margin: -20px;
      padding: 75px 50px 50px;
      background: #cdcdcd;
      -webkit-font-smoothing: antialiased;
      /* to stop flickering of text in safari */
      transform-origin: 0% 0%;
      transform: translateX(0%);
      transition: transform 0.5s cubic-bezier(0.77,0.2,0.05,1.0);
    }
  
    .menu-container input:checked ~ .menu {
      transform: translateX(0%);
    }
  }
  
  /* desktop styles */
  @media only screen and (min-width: 768px) { 
    .menu-container {
      width: 100%;
    }
  
    .menu-container a {
      color: #cdcdcd;
    }
  
    .menu-container input {
      display: none;
    }
  
    /* Burger menu */
    .menu-container span {
      display: none;
    }
  
    .menu {
      position: relative;
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  
    .menu ul {
      display: flex;
      padding: 0;
    }
  
    .menu li {
      padding: 0 20px;
    }
  }

/* Dropdown styles */
.menu .dropdown {
    position: relative;
    display: inline-block;
}

.menu .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.menu .arrow {
    display: inline-block;
    font-size: 12px;
    margin-left: 5px;
    margin-top: 5px;
    color: #cdcdcd;
}

.menu .dropdown-menu {
    visibility: hidden;
    display: block;
    opacity: 1 !important;
    position: absolute;
    top: 100%;
    right: 0;
    background: #232323;
    min-width: 200px;
    padding: 10px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 10000 !important;
    transition: all 0.3s ease;
}

/* Show dropdown on hover */
.menu .dropdown:hover > .dropdown-menu {
    visibility: visible;
    opacity: 1;
}

.menu .dropdown:hover .arrow {
    transform: rotate(180deg);
}

.menu .dropdown-menu li {
    padding: 0;
    display: block;
}

.menu .dropdown-menu a {
    padding: 10px 20px;
    display: block;
    font-size: 18px;
    color: #cdcdcd;
    white-space: nowrap;
}

.menu .dropdown-menu a:hover {
    background: rgba(255,255,255,0.1);
    color: #00C6A7;
}

/* Mobile styles */
@media only screen and (max-width: 767px) {
    .menu .dropdown-menu {
        position: static;
        visibility: hidden;
        opacity: 0;
        background: transparent;
        box-shadow: none;
        padding-left: 20px;
    }
    
    /* Show dropdown on mobile only when parent is hovered */
    .menu .dropdown:hover > .dropdown-menu {
        visibility: visible;
        opacity: 1;
    }
    
    .menu .dropdown-menu a {
        color: #232323;
    }
    
    .menu .dropdown-menu a:hover {
        background: transparent;
    }
}

/* Navbar container styles */
#navbar-placeholder {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
}

.menu-container {
    background: #232323;
    width: 100%;
}

/* Dropdown menu styles */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #232323;
    min-width: 200px;
    padding: 10px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 10000;
    display: none;
}

/* Ensure content is pushed down below fixed navbar */
/* #phase-4 {
    margin-top: 80px; 
} */