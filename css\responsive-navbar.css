/* Responsive Navbar Styles */
#navbar-placeholder {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    font-family: 'Alexandria', sans-serif;
    width: 100%;
    height: auto;
}

/* Overlay for when menu is open */
.menu-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 98;
}

body.menu-open .menu-overlay {
    display: block;
}

.menu-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #232323;
    color: #cdcdcd;
    padding: 15px 20px;
    z-index: 1;
    box-sizing: border-box;
    width: 100%;
}

.menu-logo {
    line-height: 0;
    margin: 0;
    z-index: 2;
    display: flex;
    align-items: center;
}

.menu-logo img {
    max-height: 40px;
    max-width: 100px;
    flex-shrink: 0;
}

.menu-container a {
    text-decoration: none;
    color: #cdcdcd;
    transition: color 0.3s ease;
}

.menu-container a:hover {
    color: #00C6A7;
}

/* Menu Toggle Button */
.menu-toggle {
    display: none;
}

.menu-btn {
    display: none;
    cursor: pointer;
    z-index: 2;
}

.menu-btn span {
    display: block;
    width: 30px;
    height: 3px;
    margin-bottom: 5px;
    position: relative;
    background: #cdcdcd;
    border-radius: 3px;
    z-index: 1;
    transform-origin: 4px 0px;
    transition: transform 0.5s cubic-bezier(0.77,0.2,0.05,1.0),
                background 0.5s cubic-bezier(0.77,0.2,0.05,1.0),
                opacity 0.55s ease;
}

/* Dropdown styles */
.menu .dropdown {
    position: relative;
}

.menu .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.menu .arrow {
    display: inline-block;
    font-size: 12px;
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.menu .dropdown-menu {
    position: absolute;
    visibility: hidden;
    opacity: 0;
    top: 100%;
    right: 0;
    background: #232323;
    min-width: 200px;
    padding: 10px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 10;
    transition: all 0.3s ease;
}

.menu .dropdown:hover > .dropdown-menu {
    visibility: visible;
    opacity: 1;
}

.menu .dropdown:hover .arrow {
    transform: rotate(180deg);
}

.menu .dropdown-menu li {
    padding: 0;
    display: block;
}

.menu .dropdown-menu a {
    padding: 10px 20px;
    display: block;
    font-size: 16px;
    color: #cdcdcd;
    white-space: nowrap;
}

.menu .dropdown-menu a:hover {
    background: rgba(255,255,255,0.1);
    color: #00C6A7;
}

/* Desktop styles */
@media only screen and (min-width: 992px) {
    body {
        padding-top: 80px;
    }

    .menu {
        display: flex;
        width: auto;
        align-items: center;
        overflow-x: visible;
    }
    
    .menu-container {
        overflow: visible;
        padding: 15px 20px;
    }
    
    .nav-links {
        display: flex;
        padding: 0;
        margin: 0;
        list-style: none;
        overflow: visible;
    }
    
    .nav-links li {
        padding: 0 20px;
        position: relative;
    }
    
    .nav-links li a {
        display: block;
        padding: 10px 0;
        font-size: 18px;
        font-family: 'Alexandria', sans-serif;
        white-space: nowrap;
    }
    
    /* Additional desktop fixes for RTL menus */
    .menu .nav-links {
        direction: rtl;
        margin-left: 0;
        margin-right: 0;
        width: auto;
    }
    
    /* Make sure dropdown menus align properly in RTL */
    .menu .dropdown-menu {
        right: 0;
        left: auto;
    }
}

/* Tablet styles */
@media only screen and (min-width: 768px) and (max-width: 991px) {
    body {
        padding-top: 70px;
    }

    .menu-container {
        padding: 12px 16px;
    }

    .menu {
        display: flex;
        width: auto;
        align-items: center;
    }
    
    .nav-links {
        display: flex;
        padding: 0;
        margin: 0;
        list-style: none;
    }
    
    .nav-links li {
        padding: 0 12px;
        position: relative;
    }
    
    .nav-links li a {
        display: block;
        padding: 8px 0;
        font-size: 16px;
        font-family: 'Alexandria', sans-serif;
    }
}

/* Mobile styles - General */
@media only screen and (max-width: 767px) {
    body {
        padding-top: 60px;
        overflow-x: hidden;
    }

    #navbar-placeholder {
        overflow-x: hidden;
    }

    .menu-container {
        padding: 10px 15px;
        align-items: center;
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
    }
    
    .menu-logo {
        position: sticky;
        left: 0;
    }
    
    .menu-logo img {
        max-height: 35px;
    }
    
    .menu-btn {
        display: block;
        position: relative;
        z-index: 100;
        margin-left: auto;
        width: 30px;
        height: 25px;
    }
    
    .menu {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        max-width: 300px;
        height: 100vh;
        margin: 0;
        padding: 80px 20px 20px;
        background: #232323;
        box-shadow: -2px 0 5px rgba(0,0,0,0.2);
        transition: right 0.3s ease;
        overflow-y: auto;
        overflow-x: hidden;
        z-index: 99;
        visibility: hidden;
    }
    
    .nav-links {
        padding: 0;
        margin: 0;
        list-style: none;
        width: 100%;
        overflow-x: hidden;
    }
    
    .nav-links li {
        padding: 0;
        margin-bottom: 15px;
        width: 100%;
    }
    
    .nav-links li a {
        padding: 10px 0;
        display: block;
        font-size: 18px;
    }
    
    .menu-toggle:checked ~ .menu {
        right: 0;
        visibility: visible;
    }
    
    /* Hamburger to X animation */
    .menu-toggle:checked ~ .menu-btn span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .menu-toggle:checked ~ .menu-btn span:nth-child(2) {
        opacity: 0;
    }
    
    .menu-toggle:checked ~ .menu-btn span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -7px);
    }
    
    /* Menu overlay */
    .menu-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 98;
    }
    
    .menu-toggle:checked ~ .menu-overlay {
        display: block;
    }
    
    /* Mobile dropdown styles */
    .menu .dropdown-menu {
        position: static;
        visibility: hidden;
        opacity: 0;
        max-height: 0;
        padding: 0 0 0 15px;
        background: transparent;
        box-shadow: none;
        transition: all 0.3s ease;
        overflow: hidden;
        width: 100%;
    }
    
    .menu .dropdown-toggle {
        justify-content: space-between;
        width: 100%;
    }
    
    .menu .dropdown:hover > .dropdown-menu,
    .menu .dropdown.active > .dropdown-menu {
        visibility: visible;
        opacity: 1;
        max-height: 500px;
        padding: 10px 0 10px 15px;
    }
    
    .menu .dropdown-menu a {
        padding: 8px 10px;
        font-size: 16px;
        width: 100%;
    }
}

/* Small mobile devices */
@media only screen and (max-width: 480px) {
    body {
        padding-top: 50px;
    }

    .menu-container {
        padding: 15px 15px;
    }
    
    .menu-logo img {
        max-height: 30px;
        max-width: 80px;
    }
    
    .menu-btn {
        width: 25px;
        height: 20px;
    }

    .menu-btn span {
        width: 25px;
        height: 2px;
        margin-bottom: 4px;
    }
    
    .menu {
        width: 85%;
        padding: 60px 15px 15px;
        overflow-x: hidden;
    }
    
    .nav-links li a {
        font-size: 16px;
    }
}

/* Fix for body scrolling */
body.menu-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
}

/* Content alignment fixes */
[style*="direction: rtl"],
.arabic-text,
[lang="ar"] {
    text-align: right;
}

.menu .nav-links {
    direction: rtl;
}

/* Fix for page links alignment */
section p[style*="text-align: right"] {
    direction: rtl;
}

/* Ensure links inside right-aligned content stay right-aligned */
section [style*="text-align: right"] a {
    text-align: right;
}

/* Ensure content is pushed down below fixed navbar */
body {
    padding-top: 70px;
} 