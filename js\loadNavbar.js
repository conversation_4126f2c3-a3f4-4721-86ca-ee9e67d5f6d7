document.addEventListener('DOMContentLoaded', function() {
    loadNavbar();

    // Handle window resize
    window.addEventListener('resize', debounce(function() {
        if (window.innerWidth > 767 && document.body.classList.contains('menu-open')) {
            const menuToggle = document.getElementById('menu-toggle');
            if (menuToggle && menuToggle.checked) {
                menuToggle.checked = false;
                document.body.classList.remove('menu-open');
            }
        }
    }, 250));
});

// Debounce function to prevent excessive function calls during resize
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(function() {
            func.apply(context, args);
        }, wait);
    };
}

function loadNavbar() {
    fetch('./navbar.html')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(data => {
            const navbarPlaceholder = document.getElementById('navbar-placeholder');
            if (navbarPlaceholder) {
                navbarPlaceholder.innerHTML = data;
                // Use a small delay to ensure DOM is fully updated
                setTimeout(initNavbarFunctionality, 150); 
            }
        })
        .catch(error => {
            console.error('Error loading navbar:', error);
        });
}

function initNavbarFunctionality() {
    // Handle mobile menu toggle
    const menuToggle = document.getElementById('menu-toggle');
    const menuOverlay = document.querySelector('.menu-overlay');
    const menu = document.querySelector('.menu');
    
    if (menuToggle) {
        menuToggle.addEventListener('change', function() {
            if (this.checked) {
                document.body.classList.add('menu-open');
                // Prevent background scrolling
                document.body.style.top = `-${window.scrollY}px`;
            } else {
                document.body.classList.remove('menu-open');
                // Restore scroll position
                const scrollY = document.body.style.top;
                document.body.style.top = '';
                window.scrollTo(0, parseInt(scrollY || '0') * -1);
            }
        });
    }
    
    // Close menu when overlay is clicked
    if (menuOverlay) {
        menuOverlay.addEventListener('click', function() {
            if (menuToggle && menuToggle.checked) {
                menuToggle.checked = false;
                document.body.classList.remove('menu-open');
                // Restore scroll position
                const scrollY = document.body.style.top;
                document.body.style.top = '';
                window.scrollTo(0, parseInt(scrollY || '0') * -1);
            }
        });
    }
    
    // Handle dropdown toggle on touch devices
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
        // Touch and click events for better mobile compatibility
        ['click', 'touchend'].forEach(eventType => {
            toggle.addEventListener(eventType, function(e) {
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    const dropdown = this.closest('.dropdown');
                    
                    // Toggle active class
                    if (dropdown) {
                        dropdown.classList.toggle('active');
                        
                        // Toggle arrow rotation
                        const arrow = this.querySelector('.arrow');
                        if (arrow) {
                            arrow.style.transform = dropdown.classList.contains('active') 
                                ? 'rotate(180deg)' 
                                : 'rotate(0deg)';
                        }
                    
                        // Close other open dropdowns
                        const dropdowns = document.querySelectorAll('.dropdown.active');
                        dropdowns.forEach(item => {
                            if (item !== dropdown) {
                                item.classList.remove('active');
                                // Reset other arrows
                                const otherArrow = item.querySelector('.arrow');
                                if (otherArrow) otherArrow.style.transform = 'rotate(0deg)';
                            }
                        });
                    }
                    
                    // Prevent event bubbling
                    e.stopPropagation();
                }
            }, { passive: false });
        });
    });
    
    // Add a listener to close the menu when a link is clicked
    const menuLinks = document.querySelectorAll('.menu a:not(.dropdown-toggle)');
    menuLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 767 && menuToggle && menuToggle.checked) {
                menuToggle.checked = false;
                document.body.classList.remove('menu-open');
                // Restore scroll position
                const scrollY = document.body.style.top;
                document.body.style.top = '';
                window.scrollTo(0, parseInt(scrollY || '0') * -1);
            }
        });
    });
    
    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && menuToggle && menuToggle.checked) {
            menuToggle.checked = false;
            document.body.classList.remove('menu-open');
            // Restore scroll position
            const scrollY = document.body.style.top;
            document.body.style.top = '';
            window.scrollTo(0, parseInt(scrollY || '0') * -1);
        }
    });
} 